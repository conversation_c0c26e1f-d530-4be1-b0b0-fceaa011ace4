// teacher-list-component-helper.service.ts
import { Injectable, inject } from '@angular/core';
import { Table } from 'primeng/table';
import { ISearchTeacherDto, ISpeakingLanguageDto, ITeacherTeachingLanguageDto } from 'SharedModules.Library';

import {
  IGetTeachersResponse,
  EnumDropdownOptionsService,
  IDataGridFields,
  nameOf,
  IGetTeachersRequest,
  IGenderEnum,
} from "SharedModules.Library";
import moment from 'moment-timezone';
import { Params } from "@angular/router";
import { BaseListHelperService } from './base-list-helper.service';

const ISearchTeacherDtoParamsMap = nameOf<ISearchTeacherDto>();

@Injectable({
  providedIn: 'root'
})
export class TeacherListComponentHelperService extends BaseListHelperService<
  IGetTeachersRequest,
  IGetTeachersResponse,
  ISearchTeacherDto
> {
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);

  // Default sort configuration - centralized in one place
  public static readonly DEFAULT_SORT_COLUMN = ISearchTeacherDtoParamsMap.firstName;
  public static readonly DEFAULT_SORT_DIRECTION = 'asc';
  public static readonly DEFAULT_PAGE_SIZE = 10;

  constructor() {
    super();
  }

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATIONS - Required by BaseListHelperService
  // ============================================================================

  getDefaultSortColumn(): string {
    return TeacherListComponentHelperService.DEFAULT_SORT_COLUMN;
  }

  getDefaultSortDirection(): 'asc' | 'desc' {
    return TeacherListComponentHelperService.DEFAULT_SORT_DIRECTION as 'asc' | 'desc';
  }

  getDefaultPageSize(): number {
    return TeacherListComponentHelperService.DEFAULT_PAGE_SIZE;
  }

  getEntityName(): string {
    return 'teachers';
  }

  getFieldNames(): any {
    return nameOf<IGetTeachersRequest>();
  }

  exportTeacherTable(table: Table, cols: IDataGridFields[], teachersResponse: IGetTeachersResponse) {
    // Use the base class export functionality
    this.exportTable(table, cols, teachersResponse);
  }

  /**
   * Formats a single teacher record for CSV export
   * This contains all the teacher-specific formatting logic
   * Implementation of abstract method from BaseListHelperService
   */
  formatItemForExport(teacher: ISearchTeacherDto, cols: IDataGridFields[]): Record<string, string> {
    const exportObject: Record<string, string> = {};
    const teacherFields = nameOf<ISearchTeacherDto>();

    // Initialize all column fields to empty strings to ensure consistent structure
    for (const col of cols) {
      exportObject[col.field] = '';
    }

    // Process each property explicitly based on field names from cols
    for (const col of cols) {
      const fieldName = col.field;

      if (fieldName === teacherFields.firstName) {
        exportObject[fieldName] = teacher.firstName || '';
      }
      else if (fieldName === teacherFields.lastName) {
        exportObject[fieldName] = teacher.lastName || '';
      }
      else if (fieldName === teacherFields.primaryEmail) {
        exportObject[fieldName] = teacher.primaryEmail || '';
      }
      else if (fieldName === teacherFields.gender) {
        exportObject[fieldName] = teacher.gender === 'Male' ? 'Male' : 'Female';
      }
      else if (fieldName === teacherFields.country) {
        exportObject[fieldName] = teacher.country || '';
      }
      else if (fieldName === teacherFields.isBlocked) {
        exportObject[fieldName] = teacher.isBlocked ? 'Blocked' : 'Active';
      }
      else if (fieldName === teacherFields.availabilityStatus) {
        if (teacher.availabilityStatus !== undefined) {
          try {
            const availabilityOptions = this.enumDropdownOptionsService.teacherAvailabilityStatusOptions;
            const availabilityValue = this.enumDropdownOptionsService.getLabelFromValue(availabilityOptions, teacher.availabilityStatus);
            exportObject[fieldName] = typeof availabilityValue === 'string' ? availabilityValue : String(availabilityValue || '');
          } catch (error) {
            console.warn('Error getting availability status label:', error);
            exportObject[fieldName] = String(teacher.availabilityStatus || '');
          }
        } else {
          exportObject[fieldName] = '';
        }
      }
      else if (fieldName === teacherFields.lastAvailStatusUpdate) {
        exportObject[fieldName] = teacher.lastAvailStatusUpdate ? new Date(teacher.lastAvailStatusUpdate).toLocaleDateString() : '';
      }
      else if (fieldName === teacherFields.speakingLanguages) {
        exportObject[fieldName] = this.formatArrayForExport(teacher.speakingLanguages, 'language');
      }
      else if (fieldName === teacherFields.teacherTeachingLanguages) {
        exportObject[fieldName] = this.formatArrayForExport(teacher.teacherTeachingLanguages, 'teachingLanguageName');
      }
      else if (fieldName === teacherFields.vacationDates) {
        if (Array.isArray(teacher.vacationDates) && teacher.vacationDates.length > 0) {
          exportObject[fieldName] = teacher.vacationDates
            .map(date => this.formatDateForExport(date))
            .filter(date => date)
            .join(' | ');
        } else {
          exportObject[fieldName] = '';
        }
      }
      else if (fieldName === teacherFields.activePackagesCount) {
        exportObject[fieldName] = teacher.activePackagesCount?.toString() || '0';
      }
      else if (fieldName === teacherFields.activeStudentsCount) {
        exportObject[fieldName] = teacher.activeStudentsCount?.toString() || '0';
      }
      else if (fieldName === teacherFields.accountStatus) {
        exportObject[fieldName] = teacher.accountStatus !== undefined ? String(teacher.accountStatus) : '';
      }
      else if (fieldName === teacherFields.lastAccountStatusUpdate) {
        exportObject[fieldName] = this.formatDateForExport(teacher.lastAccountStatusUpdate);
      }
      else if (fieldName === teacherFields.approvedDate) {
        exportObject[fieldName] = this.formatDateForExport(teacher.approvedDate);
      }
      else {
        // For any other fields, use the base class helper
        const value = teacher[fieldName as keyof typeof teacher];
        exportObject[fieldName] = this.getSafeStringValue(value);
      }
    }

    return exportObject;
  }



  createDefaultRequest(): IGetTeachersRequest {
    const config = this.getDefaultRequestConfig();
    return {
      pageNumber: config.pageNumber!,
      pageSize: config.pageSize!,
      sortColumn: config.sortColumn,
      sortDirection: config.sortDirection,
      searchTerm: null,
      gender: IGenderEnum.None,
      approvedDateFrom: null,
      approvedDateTo: null,
      teachingLanguage: null,
      speakingLanguage: null,
      includeBlocked: false,
      accountStatus: null,
      availabilityStatus: null,
      teachingAgesExperience: null,
      teacherStudentAgesPreference: null,
      studentAgesMin: 2,
      studentAgesMax: 17,
    };
  }

  /**
   * Creates a default IGetTeachersRequest object with standard values
   * @param searchTeacherDtoFieldNames Optional object containing field names to customize the default sortColumn
   * @returns IGetTeachersRequest with default values
   */
  createDefaultTeachersRequest(searchTeacherDtoFieldNames?: ISearchTeacherDto): IGetTeachersRequest {
    const request = this.createDefaultRequest();
    if (searchTeacherDtoFieldNames?.firstName) {
      request.sortColumn = searchTeacherDtoFieldNames.firstName;
    }
    return request;
  }

  /**
   * Maps URL query parameters to an IGetTeachersRequest object
   * @param params Route parameters from ActivatedRoute
   * @param defaultRequest Optional default request to use as base (will create one if not provided)
   * @returns IGetTeachersRequest populated with values from URL parameters
   */
  mapQueryParamsToTeachersRequest(params: Params, defaultRequest?: IGetTeachersRequest): IGetTeachersRequest {
    // Use the base class method for common parameters, then add teacher-specific ones
    return this.mapQueryParamsToRequest(params, defaultRequest);
  }

  /**
   * Override to handle teacher-specific URL parameters
   */
  protected override mapEntitySpecificParams(params: Params, request: IGetTeachersRequest): void {
    const paramsMap = nameOf<IGetTeachersRequest>();

    // Teacher-specific parameters
    if (params[paramsMap.gender] !== undefined) {
      request.gender = +params[paramsMap.gender];
    }
    if (params[paramsMap.approvedDateFrom!] !== undefined && params[paramsMap.approvedDateFrom!] !== 'null') {
      request.approvedDateFrom = new Date(params[paramsMap.approvedDateFrom!]);
    }
    if (params[paramsMap.approvedDateTo!] !== undefined && params[paramsMap.approvedDateTo!] !== 'null') {
      request.approvedDateTo = new Date(params[paramsMap.approvedDateTo!]);
    }
    if (params[paramsMap.teachingLanguage!] !== undefined && params[paramsMap.teachingLanguage!] !== 'null') {
      request.teachingLanguage = params[paramsMap.teachingLanguage!];
    }
    if (params[paramsMap.speakingLanguage!] !== undefined && params[paramsMap.speakingLanguage!] !== 'null') {
      request.speakingLanguage = params[paramsMap.speakingLanguage!];
    }
    if (params[paramsMap.includeBlocked!] !== undefined) {
      request.includeBlocked = params[paramsMap.includeBlocked!] === 'true';
    }
    if (params[paramsMap.accountStatus!] !== undefined && params[paramsMap.accountStatus!] !== 'null') {
      request.accountStatus = +params[paramsMap.accountStatus!];
    }
    if (params[paramsMap.availabilityStatus!] !== undefined && params[paramsMap.availabilityStatus!] !== 'null') {
      request.availabilityStatus = +params[paramsMap.availabilityStatus!];
    }
    if (params[paramsMap.teachingAgesExperience!] !== undefined && params[paramsMap.teachingAgesExperience!] !== 'null') {
      request.teachingAgesExperience = +params[paramsMap.teachingAgesExperience!];
    }
    if (params[paramsMap.teacherStudentAgesPreference!] !== undefined && params[paramsMap.teacherStudentAgesPreference!] !== 'null') {
      request.teacherStudentAgesPreference = +params[paramsMap.teacherStudentAgesPreference!];
    }
    if (params[paramsMap.studentAgesMin] !== undefined) {
      request.studentAgesMin = +params[paramsMap.studentAgesMin];
    }
    if (params[paramsMap.studentAgesMax] !== undefined) {
      request.studentAgesMax = +params[paramsMap.studentAgesMax];
    }
  }

  initializeTableColumns(): IDataGridFields[] {
    const searchTeacherDtoFieldNames = nameOf<ISearchTeacherDto>();

    return [
      { field: searchTeacherDtoFieldNames.firstName, header: 'First Name', sortable: true },
      { field: searchTeacherDtoFieldNames.lastName, header: 'Last Name', sortable: true },
      { field: searchTeacherDtoFieldNames.primaryEmail, header: 'Email', sortable: true },
      { field: searchTeacherDtoFieldNames.gender, header: 'Gender', sortable: true },
      { field: searchTeacherDtoFieldNames.country, header: 'Country', sortable: true, maxWidth: "200px" },
      { field: searchTeacherDtoFieldNames.city, header: 'City', sortable: false, maxWidth: "120px" },
      { field: searchTeacherDtoFieldNames.speakingLanguages, header: 'Native Speaking Languages', sortable: false },
      { field: searchTeacherDtoFieldNames.teacherTeachingLanguages, header: 'Teaching Languages', sortable: false },
      // {field: searchTeacherDtoFieldNames.dateOfRegistration!, header: 'Registration Date', sortable: true},
      // { field: searchTeacherDtoFieldNames.activePackagesCount, header: 'Active Packages', sortable: true },
      // { field: searchTeacherDtoFieldNames.activeStudentsCount, header: 'Active Students', sortable: true },
      { field: searchTeacherDtoFieldNames.vacationDates, header: 'Vacation Days', sortable: false, maxWidth: "300px" },
      { field: searchTeacherDtoFieldNames.availabilityStatus, header: 'Availability Status', sortable: true },
      { field: searchTeacherDtoFieldNames.lastAccountStatusUpdate!, header: 'Last Acc Status Update', sortable: false, hide: true }, // Hidden by default
      { field: searchTeacherDtoFieldNames.approvedDate!, header: 'Approved Date', sortable: true },
      { field: searchTeacherDtoFieldNames.accountStatus, header: 'Account Status', sortable: true } // Hidden by default
    ];
  }

}
