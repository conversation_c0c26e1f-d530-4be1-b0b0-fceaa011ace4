// ============================================================================
// PACKAGES FEATURE COMPONENT - Main container for packages feature
// ============================================================================

import { Component, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';

/**
 * Packages Feature Component
 * 
 * Main container component for the packages feature.
 * Provides routing outlet for packages-related pages:
 * - packages/list - Packages list with filtering and management
 * - packages/:id - Individual package details (future)
 * - packages/create - Create new package (future)
 */
@Component({
  selector: 'app-packages',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet
  ],
  template: `
    <div class="packages-feature-container">
      <router-outlet></router-outlet>
    </div>
  `,
  styles: [`
    .packages-feature-container {
      height: 100%;
      width: 100%;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PackagesComponent {
  // This component serves as a container for packages-related routes
  // All specific functionality is handled by child components
}
