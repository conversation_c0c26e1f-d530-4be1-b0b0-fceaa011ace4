<!-- ============================================================================ -->
<!-- PACKAGES LIST FILTERS COMPONENT TEMPLATE -->
<!-- ============================================================================ -->

<div class="packages-filters-container">
  <div class="filters-content">
    
    <!-- Search Filter -->
    <div class="filter-group">
      <label class="filter-label">Search</label>
      <input
        type="text"
        pInputText
        placeholder="Search packages..."
        [(ngModel)]="searchTerm"
        (ngModelChange)="emitFiltersChange()"
        class="w-full" />
    </div>

    <!-- Package Type Filter -->
    <div class="filter-group">
      <label class="filter-label">Package Type</label>
      <p-multiSelect
        [options]="packageTypes()"
        [(ngModel)]="selectedPackageTypes"
        (ngModelChange)="emitFiltersChange()"
        optionLabel="label"
        optionValue="value"
        placeholder="Select package types"
        [showClear]="true"
        [filter]="false"
        styleClass="w-full">
      </p-multiSelect>
    </div>

    <!-- Package Status Filter -->
    <div class="filter-group">
      <label class="filter-label">Package Status</label>
      <p-multiSelect
        [options]="packageStatuses()"
        [(ngModel)]="selectedPackageStatuses"
        (ngModelChange)="emitFiltersChange()"
        optionLabel="label"
        optionValue="value"
        placeholder="Select package statuses"
        [showClear]="true"
        [filter]="false"
        styleClass="w-full">
      </p-multiSelect>
    </div>

    <!-- Teacher Selection -->
    <div class="filter-group">
      <label class="filter-label">Teacher</label>
      <lib-prime-teachers-selection
        [pageSize]="20"
        [enablePagination]="true"
        [enableSearch]="true"
        [autoLoadInitialData]="false"
        [searchPlaceholder]="'Search teacher...'"
        [initialSelectedId]="selectedTeacher()"
        [enableInitialItemSelection]="true"
        (itemClicked)="onTeacherSelected($event)">
      </lib-prime-teachers-selection>
    </div>

    <!-- Student Selection -->
    <div class="filter-group">
      <label class="filter-label">Student</label>
      <lib-prime-students-selection
        [pageSize]="20"
        [enablePagination]="true"
        [enableSearch]="true"
        [autoLoadInitialData]="false"
        [searchPlaceholder]="'Search student...'"
        [initialSelectedId]="selectedStudent()"
        [enableInitialItemSelection]="true"
        (itemClicked)="onStudentSelected($event)">
      </lib-prime-students-selection>
    </div>

    <!-- Date Filters Section -->
    <div class="filter-section">
      <h4 class="section-title">Date Filters</h4>
      
      <!-- Purchased Date Range -->
      <div class="date-range-group">
        <label class="filter-label">Purchased Date Range</label>
        <div class="date-range-inputs">
          <p-calendar
            [(ngModel)]="purchasedFromDate"
            (ngModelChange)="emitFiltersChange()"
            placeholder="From date"
            [showIcon]="true"
            [showClear]="true"
            dateFormat="dd/mm/yy"
            styleClass="date-input">
          </p-calendar>
          <span class="date-separator">to</span>
          <p-calendar
            [(ngModel)]="purchasedToDate"
            (ngModelChange)="emitFiltersChange()"
            placeholder="To date"
            [showIcon]="true"
            [showClear]="true"
            dateFormat="dd/mm/yy"
            styleClass="date-input">
          </p-calendar>
        </div>
      </div>

      <!-- Expiration Date Range -->
      <div class="date-range-group">
        <label class="filter-label">Expiration Date Range</label>
        <div class="date-range-inputs">
          <p-calendar
            [(ngModel)]="expiresFromDate"
            (ngModelChange)="emitFiltersChange()"
            placeholder="From date"
            [showIcon]="true"
            [showClear]="true"
            dateFormat="dd/mm/yy"
            styleClass="date-input">
          </p-calendar>
          <span class="date-separator">to</span>
          <p-calendar
            [(ngModel)]="expiresToDate"
            (ngModelChange)="emitFiltersChange()"
            placeholder="To date"
            [showIcon]="true"
            [showClear]="true"
            dateFormat="dd/mm/yy"
            styleClass="date-input">
          </p-calendar>
        </div>
      </div>
    </div>

    <!-- Boolean Filters Section -->
    <div class="filter-section">
      <h4 class="section-title">Additional Filters</h4>

      <!-- Has Add-on Extension -->
      <div class="filter-group">
        <label class="filter-label">Has Add-on Extension</label>
        <p-dropdown
          [options]="getBooleanOptions()"
          [(ngModel)]="hasAddOnExtension"
          (ngModelChange)="emitFiltersChange()"
          optionLabel="label"
          optionValue="value"
          placeholder="Select option"
          [showClear]="true"
          styleClass="w-full">
        </p-dropdown>
      </div>

      <!-- Parent Account Deleted -->
      <div class="filter-group">
        <label class="filter-label">Parent Account Deleted</label>
        <p-dropdown
          [options]="getBooleanOptions()"
          [(ngModel)]="parentAccountDeleted"
          (ngModelChange)="emitFiltersChange()"
          optionLabel="label"
          optionValue="value"
          placeholder="Select option"
          [showClear]="true"
          styleClass="w-full">
        </p-dropdown>
      </div>

      <!-- Outstanding Teacher Payments -->
      <div class="filter-group">
        <div class="checkbox-wrapper">
          <p-checkbox
            [(ngModel)]="hasOutstandingTeacherPayments"
            (ngModelChange)="emitFiltersChange()"
            [binary]="true"
            inputId="outstandingPayments">
          </p-checkbox>
          <label for="outstandingPayments" class="checkbox-label">
            Has Outstanding Teacher Payments
          </label>
        </div>
      </div>
    </div>

  </div>

  <!-- Filter Actions -->
  <div class="filter-actions">
    <button
      type="button"
      pButton
      label="Reset All"
      icon="pi pi-refresh"
      class="p-button-outlined p-button-secondary"
      [disabled]="!hasActiveFilters()"
      (click)="onResetFilters()">
    </button>
    <button
      type="button"
      pButton
      label="Apply Filters"
      icon="pi pi-check"
      class="p-button"
      (click)="onApplyFilters()">
    </button>
  </div>
</div>
