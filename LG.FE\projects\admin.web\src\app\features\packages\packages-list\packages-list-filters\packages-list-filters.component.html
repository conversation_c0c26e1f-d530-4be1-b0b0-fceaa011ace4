<!-- Packages List Filters Content Only -->
<div class="filters-content">
  <div class="grid">

    <!-- Basic Filters -->
    <div class="col-12">
      <div class="flex flex-column gap-1">
        <!-- Search Filter -->
        <div class="field">
          <label for="searchTerm" class="block text-sm mb-2">Search</label>
          <input
            id="searchTerm"
            type="text"
            pInputText
            placeholder="Search packages..."
            [ngModel]="currentFilters().searchTerm"
            (ngModelChange)="onFilterChange('searchTerm', $event)"
            class="w-full" />
        </div>

    <!-- Package Type Filter -->
        <div class="field">
          <label for="packageType" class="block text-sm mb-2">Package Type</label>
          <p-select
            id="packageType"
            [options]="packageTypes()"
            [ngModel]="currentFilters().packageType"
            (ngModelChange)="onFilterChange('packageType', $event)"
            optionLabel="label"
            optionValue="value"
            placeholder="Select Package Type"
            class="w-full">
          </p-select>
        </div>

        <!-- Package Status Filter -->
        <div class="field">
          <label for="packageStatus" class="block text-sm mb-2">Package Status</label>
          <p-select
            id="packageStatus"
            [options]="packageStatuses()"
            [ngModel]="currentFilters().packageStatus"
            (ngModelChange)="onFilterChange('packageStatus', $event)"
            optionLabel="label"
            optionValue="value"
            placeholder="Select Package Status"
            class="w-full">
          </p-select>
        </div>
      </div>
    </div>

    <!-- Selection Filters -->
    <div class="col-12">
      <div class="flex flex-column gap-1">
        <!-- Teacher Selection -->
        <div class="field">
          <label for="teacher" class="block text-sm mb-2">Teacher</label>
          <lib-prime-teachers-selection
            [pageSize]="20"
            [enablePagination]="true"
            [enableSearch]="true"
            [autoLoadInitialData]="false"
            [searchPlaceholder]="'Search teacher...'"
            [initialSelectedId]="currentFilters().teacherId!"
            [enableInitialItemSelection]="true"
            (itemClicked)="onTeacherSelected($event)">
          </lib-prime-teachers-selection>
        </div>

        <!-- Student Selection -->
        <div class="field">
          <label for="student" class="block text-sm mb-2">Student</label>
          <lib-prime-students-selection
            [pageSize]="20"
            [enablePagination]="true"
            [enableSearch]="true"
            [autoLoadInitialData]="false"
            [searchPlaceholder]="'Search student...'"
            [initialSelectedId]="currentFilters().studentId!"
            [enableInitialItemSelection]="true"
            (itemClicked)="onStudentSelected($event)">
          </lib-prime-students-selection>
        </div>
      </div>
    </div>

    <!-- Date Filters -->
    <div class="col-12">
      <div class="flex flex-column gap-1">
        <h4 class="text-sm font-semibold mb-2">Date Filters</h4>

        <!-- Purchased Date Range -->
        <div class="field">
          <label class="block text-sm mb-2">Purchased Date Range</label>
          <div class="flex gap-2 align-items-center">
            <p-datepicker
              [ngModel]="currentFilters().purchasedFrom"
              (ngModelChange)="onFilterChange('purchasedFrom', $event)"
              placeholder="From date"
              [showIcon]="true"
              [showClear]="true"
              dateFormat="dd/mm/yy"
              class="flex-1">
            </p-datepicker>
            <span class="text-sm">to</span>
            <p-datepicker
              [ngModel]="currentFilters().purchasedTo"
              (ngModelChange)="onFilterChange('purchasedTo', $event)"
              placeholder="To date"
              [showIcon]="true"
              [showClear]="true"
              dateFormat="dd/mm/yy"
              class="flex-1">
            </p-datepicker>
          </div>
        </div>

        <!-- Expiration Date Range -->
        <div class="field">
          <label class="block text-sm mb-2">Expiration Date Range</label>
          <div class="flex gap-2 align-items-center">
            <p-datepicker
              [ngModel]="currentFilters().expiresFrom"
              (ngModelChange)="onFilterChange('expiresFrom', $event)"
              placeholder="From date"
              [showIcon]="true"
              [showClear]="true"
              dateFormat="dd/mm/yy"
              class="flex-1">
            </p-datepicker>
            <span class="text-sm">to</span>
            <p-datepicker
              [ngModel]="currentFilters().expiresTo"
              (ngModelChange)="onFilterChange('expiresTo', $event)"
              placeholder="To date"
              [showIcon]="true"
              [showClear]="true"
              dateFormat="dd/mm/yy"
              class="flex-1">
            </p-datepicker>
          </div>
        </div>
      </div>
    </div>

    <!-- Boolean Filters -->
    <div class="col-12">
      <div class="flex flex-column gap-1">
        <h4 class="text-sm font-semibold mb-2">Additional Filters</h4>

        <!-- Has Add-on Extension -->
        <div class="field">
          <label for="hasAddOnExtension" class="block text-sm mb-2">Has Add-on Extension</label>
          <p-select
            id="hasAddOnExtension"
            [options]="getBooleanOptions()"
            [ngModel]="currentFilters().hasAddOnExtension"
            (ngModelChange)="onFilterChange('hasAddOnExtension', $event)"
            optionLabel="label"
            optionValue="value"
            placeholder="Select option"
            class="w-full">
          </p-select>
        </div>

        <!-- Parent Account Deleted -->
        <div class="field">
          <label for="parentAccountDeleted" class="block text-sm mb-2">Parent Account Deleted</label>
          <p-select
            id="parentAccountDeleted"
            [options]="getBooleanOptions()"
            [ngModel]="currentFilters().parentAccountDeleted"
            (ngModelChange)="onFilterChange('parentAccountDeleted', $event)"
            optionLabel="label"
            optionValue="value"
            placeholder="Select option"
            class="w-full">
          </p-select>
        </div>

        <!-- Outstanding Teacher Payments -->
        <div class="field">
          <div class="flex align-items-center">
            <p-checkbox
              [ngModel]="currentFilters().hasOutstandingTeacherPayments"
              (ngModelChange)="onFilterChange('hasOutstandingTeacherPayments', $event)"
              [binary]="true"
              inputId="outstandingPayments">
            </p-checkbox>
            <label for="outstandingPayments" class="ml-2 text-sm">
              Has Outstanding Teacher Payments
            </label>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
