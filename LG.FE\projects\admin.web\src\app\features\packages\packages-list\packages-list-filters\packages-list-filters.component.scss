// ============================================================================
// PACKAGES LIST FILTERS COMPONENT STYLES
// ============================================================================

.packages-filters-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem;

  .filters-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 1rem;
  }

  .filter-actions {
    display: flex;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid var(--surface-border);
    margin-top: auto;

    .p-button {
      flex: 1;
    }
  }
}

// ============================================================================
// FILTER GROUP STYLES
// ============================================================================

.filter-group {
  margin-bottom: 1.5rem;

  .filter-label {
    display: block;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  .checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .checkbox-label {
      font-weight: 500;
      color: var(--text-color);
      cursor: pointer;
      margin: 0;
    }
  }
}

// ============================================================================
// FILTER SECTION STYLES
// ============================================================================

.filter-section {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);

  &:last-child {
    border-bottom: none;
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--surface-200);
  }
}

// ============================================================================
// DATE RANGE STYLES
// ============================================================================

.date-range-group {
  margin-bottom: 1.5rem;

  .date-range-inputs {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;

    .date-input {
      flex: 1;
      min-width: 140px;
    }

    .date-separator {
      font-size: 0.875rem;
      color: var(--text-color-secondary);
      font-weight: 500;
      white-space: nowrap;
    }
  }
}

// ============================================================================
// RESPONSIVE DESIGN
// ============================================================================

@media (max-width: 768px) {
  .packages-filters-container {
    padding: 0.75rem;

    .filter-actions {
      gap: 0.5rem;
      padding-top: 0.75rem;
    }
  }

  .filter-group {
    margin-bottom: 1.25rem;
  }

  .filter-section {
    margin-bottom: 1.5rem;

    .section-title {
      font-size: 0.9375rem;
    }
  }

  .date-range-group .date-range-inputs {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;

    .date-separator {
      text-align: center;
      padding: 0.25rem 0;
    }

    .date-input {
      min-width: unset;
    }
  }
}

@media (max-width: 576px) {
  .packages-filters-container {
    padding: 0.5rem;
  }

  .filter-group {
    margin-bottom: 1rem;

    .filter-label {
      font-size: 0.8125rem;
    }
  }

  .filter-section {
    margin-bottom: 1.25rem;

    .section-title {
      font-size: 0.875rem;
      margin-bottom: 0.75rem;
    }
  }

  .filter-actions {
    flex-direction: column;
    gap: 0.5rem;

    .p-button {
      width: 100%;
    }
  }
}

// ============================================================================
// PRIMENG COMPONENT OVERRIDES
// ============================================================================

:host ::ng-deep {
  // MultiSelect styling
  .p-multiselect {
    .p-multiselect-label {
      font-size: 0.875rem;
    }

    .p-multiselect-token {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }
  }

  // Dropdown styling
  .p-dropdown {
    .p-dropdown-label {
      font-size: 0.875rem;
    }
  }

  // Calendar styling
  .p-calendar {
    .p-inputtext {
      font-size: 0.875rem;
    }
  }

  // Checkbox styling
  .p-checkbox {
    .p-checkbox-box {
      width: 1.125rem;
      height: 1.125rem;
    }
  }

  // Input text styling
  .p-inputtext {
    font-size: 0.875rem;
  }
}

// ============================================================================
// DARK MODE SUPPORT
// ============================================================================

:host-context(.dark-mode) {
  .filter-section {
    border-bottom-color: var(--surface-700);

    .section-title {
      border-bottom-color: var(--surface-600);
    }
  }

  .filter-actions {
    border-top-color: var(--surface-700);
  }
}
