import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, input, computed, type OnInit, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  ISearchPackageDto,
  IPackageStatusEnum,
  IPackageTypeEnum,
  IAddToBasketResponse,
  GeneralService,
  AuthStateService,
  StudentsDisplayComponent,
  PermissionService,
  ToastService,
  EventBusService,
  EmitEvent,
  Events,
  IOrderItemTargetTypeEnum,
  IBasketItemDto,
  IAddToBasketRequest,
  IBasicProfileInfoDto,
  TimezoneService
} from 'SharedModules.Library';
import moment from 'moment-timezone';
import { CheckoutService } from '@platform.src/app/core/services/checkout.service';
import { BuyPackageSelectionService } from '@platform.app/core/services/buy-package-selection.service';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';

@Component({
  selector: 'app-package-mini-info-card',
  imports: [
    CommonModule,
    AvatarGroupModule,
    AvatarModule,
    ButtonModule,
    TagModule,
    StudentsDisplayComponent,
    ProgressBarModule
  ],
  templateUrl: './package-mini-info-card.component.html',
  styleUrl: './package-mini-info-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PackageMiniInfoCardComponent implements OnInit {
  // Services
  readonly generalService = inject(GeneralService);
  readonly authService = inject(AuthStateService);
  readonly permissionService = inject(PermissionService);
  private readonly checkoutService = inject(CheckoutService);
  private readonly buyPackageSelectionService = inject(BuyPackageSelectionService);
  private readonly toastService = inject(ToastService);
  private readonly eventBusService = inject(EventBusService);
  private readonly timezoneService = inject(TimezoneService);
  private readonly destroyRef = inject(DestroyRef);

  // Component inputs
  item = input.required<ISearchPackageDto>();

  // User info
  private readonly userBasicInfo = this.authService.getUserBasicInfo() as IBasicProfileInfoDto;

  // ============================================================================
  // COMPUTED PROPERTIES - BASIC INFO
  // ============================================================================

  /** Package display name combining group name or language with package type */
  packageDisplayName = computed(() => {
    const pkg = this.item();
    const languageName = pkg.teachingLanguageName || 'Language Package';
    const packageType = this.getPackageTypeDisplayText();

    if (pkg.groupName && pkg.groupName.trim() !== '') {
      return `${pkg.groupName} - ${packageType}`;
    }

    return `${languageName} ${packageType}`;
  });

  /** Teaching language name */
  languageName = computed(() => this.item().teachingLanguageName);

  /** Number of students in the package */
  studentsCount = computed(() => this.item().numberOfStudents);

  // ============================================================================
  // COMPUTED PROPERTIES - STATUS & TYPE
  // ============================================================================

  /** Package status display text */
  packageStatusText = computed(() => {
    const status = this.item().packageStatus;
    return this.generalService.getEnumDisplayText(status, IPackageStatusEnum);
  });

  /** Package status severity for PrimeNG tag styling */
  packageStatusSeverity = computed((): 'success' | 'info' | 'warn' | 'danger' => {
    const status = this.item().packageStatus;
    switch (status) {
      case IPackageStatusEnum.Active: return 'success';
      case IPackageStatusEnum.InActive: return 'warn';
      case IPackageStatusEnum.Expired: return 'danger';
      case IPackageStatusEnum.Completed: return 'info';
      case IPackageStatusEnum.Refunded: return 'danger';
      default: return 'info';
    }
  });

  /** Package type display text */
  packageTypeText = computed(() => {
    const type = this.item().packageType;
    return this.generalService.getEnumDisplayText(type, IPackageTypeEnum);
  });

  /** Package type severity for PrimeNG tag styling */
  packageTypeSeverity = computed(() => {
    const type = this.item().packageType;
    switch (type) {
      case IPackageTypeEnum.FreeTrial: return 'info';
      case IPackageTypeEnum.Gift: return 'success';
      case IPackageTypeEnum.Paid: return 'success';
      default: return 'info';
    }
  });

  // ============================================================================
  // COMPUTED PROPERTIES - LESSONS PROGRESS
  // ============================================================================

  /** Number of remaining lessons */
  remainingLessons = computed(() => this.item().remainingLessons);

  /** Total number of lessons in the package */
  totalLessons = computed(() => this.item().totalLessons);

  /** Lessons completion progress as percentage */
  lessonsProgress = computed(() => {
    const total = this.totalLessons();
    const remaining = this.remainingLessons();
    if (total === 0) return 0;
    return Math.round(((total - remaining) / total) * 100);
  });

  // ============================================================================
  // COMPUTED PROPERTIES - DATES (TIMEZONE-AWARE)
  // ============================================================================

  /** Expiration date converted to user's timezone */
  expirationDate = computed(() => {
    const pkg = this.item();
    if (!pkg.expiresOnDateUtc) return null;

    const userTimezone = this.userBasicInfo?.timeZoneIana;
    if (userTimezone) {
      return moment.utc(pkg.expiresOnDateUtc).tz(userTimezone).toDate();
    }

    return moment.utc(pkg.expiresOnDateUtc).toDate();
  });

  /** Formatted expiration date string for display in user's timezone */
  expirationDateFormatted = computed(() => {
    const expirationDate = this.expirationDate();
    if (!expirationDate) return null;

    const userTimezone = this.userBasicInfo?.timeZoneIana;
    if (userTimezone) {
      const utcDate = this.item().expiresOnDateUtc!;
      return this.timezoneService.formatInTimezone(utcDate, 'MMM D, YYYY HH:mm', userTimezone);
    }

    // Fallback formatting
    return expirationDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit'
    });
  });

  /** Package creation date in user's timezone */
  dateCreated = computed(() => {
    const pkg = this.item();
    if (!pkg.dateCreatedUtc) return null;

    const userTimezone = this.userBasicInfo?.timeZoneIana;
    if (userTimezone) {
      return this.timezoneService.convertUtcToLocal(pkg.dateCreatedUtc, {
        timezone: userTimezone,
        outputFormat: 'date'
      });
    }

    return new Date(pkg.dateCreatedUtc);
  });

  /** Package status last modified date in user's timezone */
  packageStatusLastModified = computed(() => {
    const pkg = this.item();
    const userTimezone = this.userBasicInfo?.timeZoneIana;

    if (userTimezone) {
      return this.timezoneService.convertUtcToLocal(pkg.packageStatusLastModified, {
        timezone: userTimezone,
        outputFormat: 'date'
      });
    }

    return new Date(pkg.packageStatusLastModified);
  });

  // ============================================================================
  // COMPUTED PROPERTIES - EXPIRATION LOGIC
  // ============================================================================

  /** Whether the package is expiring soon (within 14 days) */
  isExpiringSoon = computed(() => {
    const expirationDate = this.expirationDate();
    if (!expirationDate) return false;

    const userTimezone = this.userBasicInfo?.timeZoneIana;
    const now = userTimezone
      ? this.timezoneService.getCurrentTimeInZone(userTimezone)
      : new Date();

    const daysUntilExpiration = Math.ceil((expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiration <= 14 && daysUntilExpiration > 0;
  });

  /** Number of days until package expiration */
  daysUntilExpiration = computed(() => {
    const expirationDate = this.expirationDate();
    if (!expirationDate) return null;

    const userTimezone = this.userBasicInfo?.timeZoneIana;
    const now = userTimezone
      ? this.timezoneService.getCurrentTimeInZone(userTimezone)
      : new Date();

    return Math.ceil((expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  });

  /** Whether the package has been extended */
  hasExtension = computed(() => this.item().hasBeenExtended);

  /** Teacher's full name or default message */
  teacherName = computed(() => {
    const teacher = this.item().teacher;
    return teacher ? `${teacher.firstName} ${teacher.lastName}`.trim() : 'No Teacher Assigned';
  });

  /** Whether the package can be extended */
  canExtendPackage = computed(() => {
    const pkg = this.item();
    const expirationDate = this.expirationDate();

    // Cannot extend if: no expiration date, already extended, or not active
    if (!expirationDate ||
      pkg.hasBeenExtended === true ||
      pkg.packageStatus !== IPackageStatusEnum.Active) {
      return false;
    }

    // Get today's date in user's timezone for accurate comparison
    const userTimezone = this.userBasicInfo?.timeZoneIana;
    const today = userTimezone
      ? this.timezoneService.getStartOf(
        this.timezoneService.getCurrentTimeInZone(userTimezone),
        'day',
        userTimezone
      )
      : new Date();

    if (!userTimezone) {
      today.setHours(0, 0, 0, 0);
    }

    const expiration = new Date(expirationDate);
    expiration.setHours(0, 0, 0, 0);

    return today <= expiration;
  });

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  ngOnInit(): void {
    this.initializeTimezone();
  }

  /**
   * Initialize timezone service with user's timezone
   */
  private initializeTimezone(): void {
    if (this.userBasicInfo?.timeZoneIana) {
      this.timezoneService.setTimezone(this.userBasicInfo.timeZoneIana);
    }
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Get package type display text
   */
  private getPackageTypeDisplayText(): string {
    const type = this.item().packageType;
    return this.generalService.getEnumDisplayText(type, IPackageTypeEnum);
  }

  // ============================================================================
  // PUBLIC METHODS
  // ============================================================================

  /**
   * Handle manage package action
   */
  onManagePackage(): void {
    const pkg = this.item();
    if (pkg) {
      // TODO: Implement package management dialog or navigation
      console.log('Manage package:', pkg.id);
    }
  }

  /**
   * Handle extend package action
   */
  onExtendPackage(): void {
    const pkg = this.item();
    if (!pkg || !this.canExtendPackage()) {
      return;
    }

    const basketItemRequest: IAddToBasketRequest = {
      basketItem: {
        targetId: pkg.id!,
        targetType: IOrderItemTargetTypeEnum.Extension,
        teachingLanguageId: null,
        numberOfLessons: null,
        durationInMinutes: null,
        hasAddOnExtension: false,
        hasFlag: false,
        quantity: null,
        isNewLanguageForStudent: null,
        packageToExtend: null,
        existingTeacherId: null,
        wantsToChangeTeacher: null
      } as IBasketItemDto,
    };

    this.buyPackageSelectionService.addToBasket(basketItemRequest)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response: IAddToBasketResponse) => {
          this.handleExtensionSuccess(response);
        },
        error: (error) => {
          this.handleExtensionError(error);
        }
      });
  }

  /**
   * Handle successful extension addition to basket
   */
  private handleExtensionSuccess(_response: IAddToBasketResponse): void {
    this.toastService.show({
      severity: 'success',
      summary: 'Extension Added',
      detail: 'Package extension has been added to your basket successfully.'
    });

    // Refresh basket and navigate to checkout
    this.eventBusService.emit(new EmitEvent(Events.StateLoadGetBasket, {
      parentId: this.authService.getUserClaims()?.id
    }));

    this.checkoutService.goToCheckoutStep(1);
  }

  /**
   * Handle extension addition error
   */
  private handleExtensionError(error: unknown): void {
    console.error('Error adding extension to basket:', error);

    this.toastService.show({
      severity: 'error',
      summary: 'Extension Failed',
      detail: 'Failed to add package extension to basket. Please try again.'
    });
  }
}
