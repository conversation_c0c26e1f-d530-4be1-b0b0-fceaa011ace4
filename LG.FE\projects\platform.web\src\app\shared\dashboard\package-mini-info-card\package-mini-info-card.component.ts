import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, input, computed, type OnInit, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  ISearchPackageDto,
  IPackageStatusEnum,
  IPackageTypeEnum,
  IAddToBasketResponse,
  GeneralService,
  EnumDropdownOptionsService,
  AuthStateService,
  StudentsDisplayComponent,
  PermissionService,
  ToastService,
  EventBusService,
  EmitEvent,
  Events,
  IOrderItemTargetTypeEnum,
  IBasketItemDto,
  IAddToBasketRequest,
  IBasicProfileInfoDto,
  TimezoneService
} from 'SharedModules.Library';
import { CheckoutService } from '@platform.src/app/core/services/checkout.service';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';
import { BuyPackageSelectionService } from '@platform.app/core/services/buy-package-selection.service';

@Component({
  selector: 'app-package-mini-info-card',
  imports: [
    CommonModule,
    AvatarGroupModule,
    AvatarModule,
    ButtonModule,
    TagModule,
    StudentsDisplayComponent,
    ProgressBarModule
  ],
  templateUrl: './package-mini-info-card.component.html',
  styleUrl: './package-mini-info-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PackageMiniInfoCardComponent implements OnInit {
  generalService = inject(GeneralService);
  enumDropdownService = inject(EnumDropdownOptionsService);
  authService = inject(AuthStateService);
  permissionService = inject(PermissionService);
  checkoutService = inject(CheckoutService);
  buyPackageSelectionService = inject(BuyPackageSelectionService);
  toastService = inject(ToastService);
  eventBusService = inject(EventBusService);
  timezoneService = inject(TimezoneService);
  private destroyRef = inject(DestroyRef);

  userBasicInfo = this.authService.getUserBasicInfo() as IBasicProfileInfoDto;
  // Accept ISearchPackageDto
  item = input.required<ISearchPackageDto>();

  // Initialize timezone service with user's timezone
  private initializeTimezone() {
    if (this.userBasicInfo?.timeZoneIana) {
      this.timezoneService.setTimezone(this.userBasicInfo.timeZoneIana);
    }
  }

  // Computed properties for better UX
  packageDisplayName = computed(() => {
    const pkg = this.item();

    // Create display name based on teaching language and package type
    const languageName = pkg.teachingLanguageName || 'Language Package';
    const packageType = this.getPackageTypeDisplayText();

    if (pkg.groupName && pkg.groupName.trim() !== '') {
      return `${pkg.groupName} - ${packageType}`;
    }

    return `${languageName} ${packageType}`;
  });

  languageName = computed(() => this.item().teachingLanguageName);

  studentsCount = computed(() => this.item().numberOfStudents);

  packageStatusText = computed(() => {
    const status = this.item().packageStatus;
    return this.generalService.getEnumDisplayText(status, IPackageStatusEnum);
  });

  packageStatusSeverity = computed((): 'success' | 'info' | 'warn' | 'danger' => {
    const status = this.item().packageStatus;
    // Map status to PrimeNG tag severity based on IPackageStatusEnum
    switch (status) {
      case IPackageStatusEnum.Active: return 'success'; // Active
      case IPackageStatusEnum.InActive: return 'warn'; // Inactive
      case IPackageStatusEnum.Expired: return 'danger'; // Expired
      case IPackageStatusEnum.Completed: return 'info'; // Completed
      case IPackageStatusEnum.Refunded: return 'danger'; // Refunded
      default: return 'info';
    }
  });

  packageTypeText = computed(() => {
    const type = this.item().packageType;
    return this.generalService.getEnumDisplayText(type, IPackageTypeEnum);
  });

  packageTypeSeverity = computed(() => {
    const type = this.item().packageType;
    switch (type) {
      case IPackageTypeEnum.FreeTrial: return 'info'; // Free Trial
      case IPackageTypeEnum.Gift: return 'success'; // Gift
      case IPackageTypeEnum.Paid: return 'success'; // Paid - using contrast instead of primary
      default: return 'info';
    }
  });

  remainingLessons = computed(() => this.item().remainingLessons);
  totalLessons = computed(() => this.item().totalLessons);

  lessonsProgress = computed(() => {
    const total = this.totalLessons();
    const remaining = this.remainingLessons();
    if (total === 0) return 0;
    return Math.round(((total - remaining) / total) * 100);
  });

  expirationDate = computed(() => {
    const pkg = this.item();
    if (!pkg.expiresOnDateUtc) return null;

    // Convert UTC date to user's timezone
    const userTimezone = this.userBasicInfo?.timeZoneIana;
    if (userTimezone) {
      return this.timezoneService.convertUtcToLocal(pkg.expiresOnDateUtc, {
        timezone: userTimezone,
        outputFormat: 'date',
      });
    }

    // Fallback to UTC date if no timezone info
    return new Date(pkg.expiresOnDateUtc);
  });

  // Formatted expiration date string for display
  expirationDateFormatted = computed(() => {
    const expirationDate = this.expirationDate();
    if (!expirationDate) return null;

    const userTimezone = this.userBasicInfo?.timeZoneIana;
    if (userTimezone) {

      return this.timezoneService.formatInTimezone(
        this.item().expiresOnDateUtc!,
        'MMM d, yyyy HH:mm',
        userTimezone
      );
    }

    // Fallback formatting
    return expirationDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit'
    });
  });

  // Additional timezone-aware date fields for potential future use
  dateCreated = computed(() => {
    const pkg = this.item();
    if (!pkg.dateCreatedUtc) return null;

    const userTimezone = this.userBasicInfo?.timeZoneIana;
    if (userTimezone) {
      return this.timezoneService.convertUtcToLocal(pkg.dateCreatedUtc, {
        timezone: userTimezone,
        outputFormat: 'date'
      });
    }

    return new Date(pkg.dateCreatedUtc);
  });

  packageStatusLastModified = computed(() => {
    const pkg = this.item();
    const userTimezone = this.userBasicInfo?.timeZoneIana;

    if (userTimezone) {
      return this.timezoneService.convertUtcToLocal(pkg.packageStatusLastModified, {
        timezone: userTimezone,
        outputFormat: 'date'
      });
    }

    return new Date(pkg.packageStatusLastModified);
  });

  isExpiringSoon = computed(() => {
    const expirationDate = this.expirationDate();
    if (!expirationDate) return false;

    // Get current time in user's timezone for accurate comparison
    const userTimezone = this.userBasicInfo?.timeZoneIana;
    const now = userTimezone
      ? this.timezoneService.getCurrentTimeInZone(userTimezone)
      : new Date();

    const daysUntilExpiration = Math.ceil((expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiration <= 14 && daysUntilExpiration > 0; // More urgent - 2 weeks instead of 30 days
  });

  daysUntilExpiration = computed(() => {
    const expirationDate = this.expirationDate();
    if (!expirationDate) return null;

    // Get current time in user's timezone for accurate comparison
    const userTimezone = this.userBasicInfo?.timeZoneIana;
    const now = userTimezone
      ? this.timezoneService.getCurrentTimeInZone(userTimezone)
      : new Date();

    return Math.ceil((expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  });

  hasExtension = computed(() => this.item().hasBeenExtended);

  teacherName = computed(() => {
    const teacher = this.item().teacher;
    return teacher ? `${teacher.firstName} ${teacher.lastName}`.trim() : 'No Teacher Assigned';
  });

  canExtendPackage = computed(() => {
    const pkg = this.item();
    const expirationDate = this.expirationDate();

    // Can extend if:
    // 1. Package has an expiration date
    // 2. Today is before the expiration date
    // 3. Package doesn't already have an extension (hasBeenExtended is false)
    // 4. Package status is Active
    // 5. Package has remaining lessons (optional check for better UX)
    if (!expirationDate ||
      pkg.hasBeenExtended === true ||
      pkg.packageStatus !== IPackageStatusEnum.Active) {
      return false;
    }

    // Get today's date in user's timezone for accurate comparison
    const userTimezone = this.userBasicInfo?.timeZoneIana;
    const today = userTimezone
      ? this.timezoneService.getStartOf(
        this.timezoneService.getCurrentTimeInZone(userTimezone),
        'day',
        userTimezone
      )
      : new Date();

    if (!userTimezone) {
      today.setHours(0, 0, 0, 0); // Reset time to start of day for accurate comparison
    }

    const expiration = new Date(expirationDate);
    expiration.setHours(0, 0, 0, 0); // Reset time to start of day

    return today <= expiration; // Allow extension on the expiration day itself
  });

  ngOnInit(): void {
    this.initializeTimezone();
  }

  private getPackageTypeDisplayText(): string {
    const type = this.item().packageType;
    return this.generalService.getEnumDisplayText(type, IPackageTypeEnum);
  }

  onManagePackage() {
    const pkg = this.item();
    if (pkg) {
      // TODO: Implement package management dialog or navigation
      console.log('Manage package:', pkg.id);
    }
  }

  onExtendPackage() {
    const pkg = this.item();
    if (!pkg || !this.canExtendPackage()) {
      return;
    }

    // Create extension basket item request
    const basketItemRequest: IAddToBasketRequest = {
      basketItem: {
        targetId: pkg.id!,
        targetType: IOrderItemTargetTypeEnum.Extension,
        teachingLanguageId: null,
        numberOfLessons: null,
        durationInMinutes: null,
        hasAddOnExtension: false,
        hasFlag: false,
        quantity: null,
        isNewLanguageForStudent: null,
        packageToExtend: null,
        existingTeacherId: null,
        wantsToChangeTeacher: null
      } as IBasketItemDto,
    };

    // Add to basket
    this.buyPackageSelectionService.addToBasket(basketItemRequest)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response: IAddToBasketResponse) => {
          this.handleExtensionSuccess(response);
        },
        error: (error) => {
          this.handleExtensionError(error);
        }
      });
  }

  private handleExtensionSuccess(_response: IAddToBasketResponse): void {
    // Show success message
    this.toastService.show({
      severity: 'success',
      summary: 'Extension Added',
      detail: 'Package extension has been added to your basket successfully.'
    });

    // Emit event to refresh basket
    this.eventBusService.emit(new EmitEvent(Events.StateLoadGetBasket, {
      parentId: this.authService.getUserClaims()?.id
    }));

    // Navigate to checkout
    this.checkoutService.goToCheckoutStep(1);
  }

  private handleExtensionError(error: any): void {
    console.error('Error adding extension to basket:', error);

    this.toastService.show({
      severity: 'error',
      summary: 'Extension Failed',
      detail: 'Failed to add package extension to basket. Please try again.'
    });
  }
}
