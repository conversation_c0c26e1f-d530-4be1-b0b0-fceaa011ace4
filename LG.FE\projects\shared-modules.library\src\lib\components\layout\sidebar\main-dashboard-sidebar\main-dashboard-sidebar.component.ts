import { CommonModule, NgOptimizedImage } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  Injector,
  input,
  Input,
  signal,
  ViewChild,
  ViewContainerRef
} from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { toObservable } from '@angular/core/rxjs-interop';

import { DropdownModule } from 'primeng/dropdown';
import { TooltipModule } from 'primeng/tooltip';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { BadgeModule } from 'primeng/badge';
import { StyleClassModule } from 'primeng/styleclass';
import { SkeletonLoaderComponent } from '../../../skeleton-loader/skeleton-loader.component';
import { LanguageLevelSelectorComponent } from '../../../language-level-selector/language-level-selector.component';
import { IParentDashboardHandlerResponse, ITimezoneData } from '../../../../GeneratedTsFiles';
import { GeneralService } from '../../../../services/general.service';
import { EmitEvent, EventBusService, Events } from '../../../../services/event-bus.service';
import { AuthStateService } from '../../../../services/auth-state.service';
import { DataApiStateService, State } from '../../../../services/data-api-state.service';
import { HandleApiResponseService } from '../../../../services/handle-api-response.service';
import { ToastService } from '../../../../services/toast.service';
import { IUserRole } from '../../../../models/general.model';
import { untilDestroyed } from '../../../../helpers/until-destroyed';
@Component({
  selector: 'app-main-dashboard-sidebar',
  imports: [
    CommonModule,
    ButtonModule,
    ScrollPanelModule,
    RouterModule,
    DropdownModule,
    TooltipModule,
    BadgeModule,
    StyleClassModule,
    SkeletonLoaderComponent,
    NgOptimizedImage,
    LanguageLevelSelectorComponent
  ],
  templateUrl: './main-dashboard-sidebar.component.html',
  styleUrl: './main-dashboard-sidebar.component.scss',
  animations: [
    trigger('submenuAnimation', [
      state('collapsed', style({
        height: '0',
        opacity: '0',
        overflow: 'hidden'
      })),
      state('expanded', style({
        height: '*',
        opacity: '1'
      })),
      transition('collapsed <=> expanded', [
        animate('200ms ease-in-out')
      ])
    ])
  ]
})
export class MainDashboardSidebarComponent {

  @Input() extraLayoutCssClass = '';

  router = inject(Router);
  generalService = inject(GeneralService);
  authService = inject(AuthStateService);
  eventBusService = inject(EventBusService);
  dataStateService = inject(DataApiStateService);
  apiService = inject(HandleApiResponseService);
  toastService = inject(ToastService);
  user = computed(() => {
    return this.authService.getUserClaims();
  });
  IUserRole = IUserRole;
  dashboardCounts$ = computed(() => this.dataStateService.getParentDashboard.state() || {} as State<IParentDashboardHandlerResponse>);

  @ViewChild('dynamicComponentContainer', {
    read: ViewContainerRef,
    static: true
  }) dynamicComponentContainer: ViewContainerRef | undefined;

  studentGroups$ = computed(() => {
    return this.dataStateService.parentStudentsGroups.state() || [];
  });
  students = [
    {
      name: 'Student 1',
      isGroup: false,
      id: 1,
      image: 'assets/images/dummy/astronaut-01.png',
    },
    {
      name: 'Student 2',
      isGroup: false,
      id: 2,
      image: 'assets/images/dummy/astronaut-01.png',
    },
    {
      name: 'Student 3',
      isGroup: false,
      id: 3,
      image: 'assets/images/dummy/astronaut-01.png',
    },
    {
      name: 'English Group',
      isGroup: true,
      id: 4,
      image: 'assets/images/dummy/astronaut-01.png',
    },
  ]
  private readonly untilDestroyed = untilDestroyed();
  private injector = inject(Injector);
  addLogoutLink = input(false);
  loadingTimezones = signal(true);
  timezones = signal<ITimezoneData[]>([]);

  navigationLinks = signal<any[]>([
    {
      path: '/dashboard/parent/overview',
      label: 'Dashboard',
      icon: 'pi pi-home'
    },
    {
      label: 'Student Management',
      icon: 'pi pi-users',
      expanded: false,
      items: [
        {
          path: '/dashboard/parent/students',
          label: 'Students',
          icon: 'pi pi-user',
          count: 3
        },
        {
          path: '/dashboard/parent/groups/list',
          label: 'Groups',
          icon: 'pi pi-users',
          count: 1
        }
      ]
    },
    {
      label: 'Learning',
      icon: 'pi pi-book',
      expanded: false,
      items: [
        {
          path: '/dashboard/parent/lessons/list',
          label: 'Lessons',
          icon: 'pi pi-book'
        },
        {
          path: '/dashboard/parent/calendar',
          label: 'Calendar',
          icon: 'pi pi-calendar'
        }
      ]
    },
    {
      path: '/dashboard/parent/packages',
      label: 'Packages',
      icon: 'pi pi-box'
    },
    {
      path: '/dashboard/parent/notifications',
      label: 'Notifications',
      icon: 'pi pi-bell'
    },
    {
      path: '/dashboard/user-settings/personal-info',
      label: 'Settings',
      icon: 'pi pi-cog'
    }
  ]);

  ngOnInit() {
    this.handleTimezonesRequest();
    console.log(this.authService.getUserRole());
    // this.openNewGroupDialog();

    // Get the user role and set navigation links accordingly
    const userRole = this.authService.getUserRole(); // Assume this method returns the user's role
    // this.setNavigationLinksByRole(userRole);
    this.initEvents();
    this.initActiveMenuState();

  }

  logout() {
    this.authService.logout();
    this.generalService.sidebarVisible.set(false);
  }

  private setNavigationLinksByRole(role: string): void {
    const commonLinks = [
      { path: '/dashboard/user-settings/account-info', label: 'Settings', icon: 'pi pi-cog' },
    ];

    let roleBasedLinks: any[] = [];

    switch (role) {

      case IUserRole.TEACHER:
        roleBasedLinks = [
          { path: '/dashboard/teacher/overview', label: 'Dashboard', icon: 'pi pi-home' },
          { path: '/dashboard/teacher/students', label: 'Students', icon: 'pi pi-user' },
          { path: '/dashboard/teacher/groups', label: 'Groups', icon: 'pi pi-users' },
          { path: '/dashboard/teacher/lessons/list', label: 'Lessons', icon: 'pi pi-book' },
          { path: '/dashboard/teacher/calendar', label: 'Calendar', icon: 'pi pi-calendar' },
          { path: '/dashboard/teacher/library', label: 'Library', icon: 'pi pi-folder' },
          { path: '/dashboard/teacher/availability/timeslots', label: 'Availability', icon: 'pi pi-clock', badgeIcon: 'pi pi-exclamation-circle' },
        ];
        break;

      case IUserRole.PARENT:
        roleBasedLinks = [
          { path: '/dashboard/parent/overview', label: 'Dashboard', icon: 'pi pi-home' },
          { path: '/dashboard/parent/students', label: 'Students', icon: 'pi pi-user', count: 0 },
          { path: '/dashboard/parent/groups/list', label: 'Groups', icon: 'pi pi-users', count: 0 },
          { path: '/dashboard/parent/lessons/list', label: 'Lessons', icon: 'pi pi-book', badgeIcon: 'pi pi-exclamation-circle' },
          { path: '/dashboard/parent/calendar', label: 'Calendar', icon: 'pi pi-calendar' },
          { path: '/dashboard/parent/library', label: 'Library', icon: 'pi pi-folder' },
          { path: '/dashboard/parent/packages', label: 'Packages', icon: 'pi pi-box' },
        ];
        break;
      case IUserRole.STUDENT:
        roleBasedLinks = [
          { path: '/dashboard/student/overview', label: 'Home', icon: 'pi pi-home' },
          { path: '/dashboard/student/groups', label: 'Groups', icon: 'pi pi-users', count: 0 },
          { path: '/dashboard/student/lessons/list', label: 'Lessons', icon: 'pi pi-book' },
          { path: '/dashboard/student/library', label: 'Library', icon: 'pi pi-folder' },
          { path: '/dashboard/student/progress', label: 'Progress', icon: 'pi pi-chart-bar' },

        ];
        break;

      case IUserRole.ADMIN:
        roleBasedLinks = [
          {
            path: '/dashboard/overview',
            label: 'Dashboard',
            icon: 'pi pi-home'
          },
          {
            label: 'Users',
            icon: 'pi pi-users',
            expanded: false,
            items: [
              {
                path: '/dashboard/students/list',
                label: 'Students',
                icon: 'pi pi-list'
              },
              {
                path: '/dashboard/teachers/list',
                label: 'Teachers',
                icon: 'pi pi-list'
              },
              {
                path: '/dashboard/groups/list',
                label: 'Groups',
                icon: 'pi pi-list'
              },
              {
                path: '/dashboard/teacher-change-requests/list',
                label: 'Teacher Change Requests',
                icon: 'pi pi-refresh'
              },
            ]
          },
          {
            label: 'General',
            icon: 'pi pi-book',
            expanded: false,
            items: [
              {
                path: '/dashboard/teachers/add',
                label: 'Register Teacher',
                icon: 'pi pi-plus'
              },
              {
                path: '/dashboard//student-registrations-list',
                label: 'Student Registrations',
                icon: 'pi pi-list'
              },
              {
                path: '/dashboard/packages',
                label: 'Packages',
                icon: 'pi pi-box'
              },
            ]
          },
          {
            label: 'Settings',
            icon: 'pi pi-cog',
            expanded: false,
            items: [
              {
                path: '/dashboard/teaching-languages',
                label: 'Teaching Languages',
                icon: 'pi pi-book'
              },
              {
                path: '/dashboard/prices',
                label: 'Prices',
                icon: 'pi pi-money-bill'
              },
            ]
          }
        ];
        break;

      default:
        break;
    }

    // Combine role-based and common links
    // Only add commonLinks if not admin role
    this.navigationLinks.set(role === IUserRole.ADMIN ?
      [...roleBasedLinks] :
      [...roleBasedLinks, ...commonLinks]
    );
    if (this.addLogoutLink()) {
      this.navigationLinks.update((links: any[]) => {
        return [...links, { method: this.logout.bind(this), label: 'Logout', icon: 'pi pi-sign-out' }];
      })
    }
    this.updateNavigationCounts();
  }

  private initActiveMenuState(): void {
    this.navigationLinks.update(links => {
      return links.map(link => {
        if (link.items) {
          // Check if any child route is active
          const hasActiveChild = link.items.some((item: any) => {
            if (item.items) {
              return item.items.some((subItem: any) => this.isActive(subItem.path));
            }
            return this.isActive(item.path);
          });

          // Set expanded state based on active child
          link.expanded = hasActiveChild;
        }
        return link;
      });
    });
  }

  toggleSubmenu(link: any, event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (link.items) {
      link.expanded = !link.expanded;
      // Close other expanded menus
      this.navigationLinks.update(links =>
        links.map(item => {
          if (item !== link && item.items) {
            item.expanded = false;
          }
          return item;
        })
      );
    }
  }

  updateNavigationCounts() {
    // Convert the signal into an observable and subscribe to its updates
    toObservable(this.dashboardCounts$, { injector: this.injector })
      .pipe(this.untilDestroyed()) // Automatically unsubscribe when the component is destroyed
      .subscribe({
        next: (dashboardCounts: State<IParentDashboardHandlerResponse>) => {
          if (dashboardCounts?.data) { // Ensure counts and data are available
            console.log('Counts data:', dashboardCounts);

            // Update navigation links signal
            this.navigationLinks.update((links: any[]) => {
              return links.map(link => {
                // Update the count for specific paths if data exists
                if (link.label === 'Students') {
                  return { ...link, count: dashboardCounts.data!.numberOfStudents || 0 };
                } else if (link.label === 'Groups') {
                  return { ...link, count: dashboardCounts.data!.numberOfGroups || 0 };
                }
                // Return the original link unchanged for other paths
                return link;
              });
            });
          } else {
            console.warn('No data available for counts.');
          }
        }
      })
  }

  onSelectListItem($event: any) {
    if ($event && $event.link) {
      this.router.navigate([$event.link]);
    }
  }

  private initEvents(): void {
    // this event is fired when user is logged in
    const userLoggedInSubscription = toObservable(this.user, {
      injector: this.injector
    }).pipe(this.untilDestroyed()).subscribe({
      next: (user) => {
        if (!user) {
          return;
        }
        this.setNavigationLinksByRole(user.role);
        if (user?.role === IUserRole.PARENT) {

          setTimeout(() => {
          }, 10);
          // this.prepareParentStudents();
        }
      }
    });
  }

  private handleTimezonesRequest(): void {

    // this.apiService.getApiData<{ timezonesData: ITimezoneData[] }>(
    //   { url: LocationDataRoutes.getTimezones, method: 'GET' },
    // ).pipe(this.untilDestroyed()).subscribe({
    //   next: (response: { timezonesData: ITimezoneData[] }) => {
    //     console.log(response);
    //     this.loadingTimezones.set(false);
    //     this.timezones.set(response.timezonesData);
    //   },
    // });
    // this.apiService.getApiData<ITimezoneData[]>(
    //   {url: '/Testing/ParentChangeTimezone', method: 'POST'},
    //   {parentId: this.authService.getUser()?.id}
    // ).pipe(this.untilDestroyed()).subscribe({
    //   next: response => {
    //     console.log(response);
    //     this.loadingTimezones.set(false);
    //     this.timezones.set(response);
    //   },
    // });

  }

  link(method: string | null) {

  }

  isRouteInSubmenu(subItem: any): boolean {
    if (!subItem || !subItem.items) return false;

    return subItem.items.some((item: any) => this.isActive(item.path));
  }

  isActive(route: string): boolean {
    if (!route) return false;
    return this.router.url.startsWith(route);
  }

  isActiveParent(link: any): boolean {
    if (!link || !link.items) return false;

    // Check if any direct child is active
    const hasActiveChild = link.items.some((item: any) => {
      // Check direct child route
      if (this.isActive(item.path)) return true;

      // Check grandchild routes if they exist
      if (item.items) {
        return item.items.some((grandChild: any) => this.isActive(grandChild.path));
      }
      return false;
    });

    return hasActiveChild;
  }

  onTimezoneChange(event: any): void {
    console.log(event.value);
    this.apiService.getApiData<ITimezoneData[]>(
      { url: 'testing/ParentChangeTimezone', method: 'POST' },
      { parentId: this.authService.getUserClaims().id, timeZoneIana: event.value.timezoneValue }
    ).pipe(this.untilDestroyed()).subscribe({
      next: (response: any) => {
        console.log(response);
        this.authService.handleUserDataAndDecodeJWT(response);
      },
    });
  }

  stopImpersonate() {
    this.eventBusService.emit(new EmitEvent(Events.StateLoadStopImpersonate, {
      studentRefreshToken: this.authService.getRefreshToken()
    }));
  }
}
