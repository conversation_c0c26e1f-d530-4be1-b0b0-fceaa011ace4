<div class="package-card surface-card shadow-2 border-round-lg p-3 hover:shadow-4 transition-all transition-duration-300">
  <!-- Header Section -->
  <div class="flex justify-content-between align-items-start gap-3 mb-3">
    <div class="flex align-items-center gap-2 flex-1">
      <img [src]="generalService.getImageUrlForLanguage(languageName())" alt="Language Flag" class="language-flag"
        [title]="languageName()" />
      <div class="flex flex-column gap-1 flex-1">
        <h3 class="text-base font-bold text-900 m-0 line-height-3">{{ packageDisplayName() }}</h3>
        
      </div>
    </div>
    <div class="flex align-items-start gap-1 flex-wrap">
      <p-tag [value]="packageTypeText()" [severity]="packageTypeSeverity()"
             styleClass="text-xs font-semibold px-2 py-1 border-round-md">
      </p-tag>
      <p-tag [value]="packageStatusText()" [severity]="packageStatusSeverity()"
             styleClass="text-xs font-semibold px-2 py-1 border-round-md">
      </p-tag>
    </div>
  </div>

  <!-- Content Section -->
  <div class="content-section">
    <!-- Students Display -->
    @if (item().students && item().students.length > 0) {
    <div class="students-section mb-3 p-2 surface-50 border-round">
      <div class="flex align-items-center gap-2 mb-2">
        <i class="pi pi-users text-primary text-sm"></i>
        <span class="text-xs font-semibold text-700">Students ({{ studentsCount() }})</span>
      </div>

      <lib-students-display
        [students]="item().students"
        [config]="{
          layout: 'horizontal',
          size: 'normal',
          showImages: true,
          showNames: true,
          showAge: false,
          showLanguages: false,
          maxVisible: 3,
          showMoreButton: true,
          styleClass: 'w-full'
        }">
      </lib-students-display>
    </div>
    }

    <!-- Progress Section -->
    <div class="progress-section mb-3 p-2 surface-50 border-round">
      <div class="flex justify-content-between align-items-center mb-2">
        <div class="flex align-items-center gap-2">
          <i class="pi pi-chart-line text-primary text-sm"></i>
          <span class="text-xs font-semibold text-700">Lessons</span>
        </div>
        <span class="text-sm font-bold text-900">{{ totalLessons() - remainingLessons() }}/{{ totalLessons() }}</span>
      </div>

      <div class="progress-container mb-2">
        <p-progressBar [value]="lessonsProgress()" [showValue]="false"
                       styleClass="modern-progress-bar h-1rem border-round">
        </p-progressBar>
      </div>

      <div class="flex justify-content-between align-items-center">
        <span class="text-xs text-600">{{ lessonsProgress() }}% completed</span>
        <span class="text-xs text-primary font-medium">{{ remainingLessons() }} remaining</span>
      </div>
    </div>

    <!-- Package Details -->
    <div class="details-section flex flex-column gap-2">
      <!-- Teacher Info -->
      <div class="detail-item p-2 surface-100 border-round flex align-items-center gap-2">
        <div class="w-2rem h-2rem bg-primary-100 border-round flex align-items-center justify-content-center">
          <i class="pi pi-user text-primary text-sm"></i>
        </div>
        <div class="flex flex-column gap-0">
          <span class="text-xs text-500 font-medium">Teacher</span>
          <span class="text-sm font-semibold text-900">{{ teacherName() }}</span>
        </div>
      </div>

      <!-- Expiration Info -->
      @if (expirationDate()) {
      <div class="detail-item p-2 border-round flex align-items-center gap-2"
           [class]="isExpiringSoon() ? 'bg-orange-50 border-1 border-orange-200' : 'surface-100'">
        <div class="w-2rem h-2rem border-round flex align-items-center justify-content-center"
             [class]="isExpiringSoon() ? 'bg-orange-100' : 'bg-blue-100'">
          <i class="pi pi-calendar text-sm"
             [class]="isExpiringSoon() ? 'text-orange-600' : 'text-blue-600'"></i>
        </div>
        <div class="flex flex-column gap-0 flex-1">
          <span class="text-xs font-medium"
                [class]="isExpiringSoon() ? 'text-orange-600' : 'text-500'">Expires</span>
          <span class="text-sm font-semibold"
                [class]="isExpiringSoon() ? 'text-orange-800' : 'text-900'">
            {{ expirationDateFormatted() }}
          </span>
        </div>
        @if (isExpiringSoon()) {
        <span class="bg-orange-500 text-white text-xs px-2 py-1 border-round-xl font-bold">SOON</span>
        } @else if (canExtendPackage()) {
        <span class="bg-green-100 text-green-700 text-xs px-2 py-1 border-round-xl font-bold">EXTENDABLE</span>
        }
      </div>
      }

      <!-- Extension Info -->
      @if (hasExtension()) {
      <div class="detail-item p-2 bg-green-50 border-1 border-green-200 border-round flex align-items-center gap-2">
        <div class="w-2rem h-2rem bg-green-100 border-round flex align-items-center justify-content-center">
          <i class="pi pi-plus-circle text-green-600 text-sm"></i>
        </div>
        <span class="text-sm font-semibold text-green-800 flex-1">Extended Package</span>
        <span class="bg-green-500 text-white text-xs px-2 py-1 border-round-xl font-bold">EXTENDED</span>
      </div>
      }
    </div>
  </div>

  <!-- Actions Section -->
  @if (this.permissionService.hasPermission(this.authService.getUserClaims(), ['packages', 'update'])) {
  <div class="actions-section pt-3 mt-3 border-top-1 surface-border">
    <div class="flex gap-2">
      <!-- Extend Package Button - Only show if package can be extended -->
      @if (canExtendPackage()) {
      <p-button
        label="Extend Package"
        icon="pi pi-plus-circle"
        class="w-full"
        styleClass="w-full p-button-primary p-button-sm flex-1 font-semibold"
        (click)="onExtendPackage()">
      </p-button>
      }

      <!-- Manage Package Button -->
    </div>
  </div>
  }
</div>