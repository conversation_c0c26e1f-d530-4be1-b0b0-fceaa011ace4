import { Injectable } from '@angular/core';
import { IGetPackagesRequest, ISearchPackageDto, nameOf } from 'SharedModules.Library';

/**
 * Helper service for packages list operations
 * Provides default configurations and utilities for package data grid operations
 */
@Injectable({
  providedIn: 'root'
})
export class PackagesListHelperService {
  fieldNames = nameOf<IGetPackagesRequest>();

  // Default pagination settings
  static readonly DEFAULT_PAGE_NUMBER = 1;
  static readonly DEFAULT_PAGE_SIZE = 10;
  static readonly DEFAULT_SORT_COLUMN = nameOf<ISearchPackageDto>().dateCreatedUtc;
  static readonly DEFAULT_SORT_DIRECTION = 'desc';

  constructor() { }

  /**
   * Creates a default IGetPackagesRequest object with standard values for backend pagination
   * @returns IGetPackagesRequest with default values optimized for pagination
   */
  createDefaultPackagesRequestForUser(): IGetPackagesRequest {
    return {
      pageNumber: PackagesListHelperService.DEFAULT_PAGE_NUMBER,
      pageSize: PackagesListHelperService.DEFAULT_PAGE_SIZE,
      sortColumn: PackagesListHelperService.DEFAULT_SORT_COLUMN,
      sortDirection: PackagesListHelperService.DEFAULT_SORT_DIRECTION,
      searchTerm: null,
      packageType: null,
      packageStatus: null,
      purchasedFrom: null,
      purchasedTo: null,
      expiresFrom: null,
      expiresTo: null,
      teacherId: null,
      parentId: null,
      studentId: null,
      groupId: null,
      teachingLanguageId: null,
      hasAddOnExtension: null,
      parentAccountDeleted: null,
      hasOutstandingTeacherPayments: false,
    };
  }

  /**
   * Clean request for API call (remove null/undefined values, format dates)
   * @param request The request to clean
   * @returns Cleaned IGetPackagesRequest with only non-null/undefined values
   */
  cleanRequestForApi(request: IGetPackagesRequest): Partial<IGetPackagesRequest> {
    const cleanedRequest: any = {};

    // Always include required pagination properties
    if (request.pageNumber !== undefined) {
      cleanedRequest.pageNumber = request.pageNumber;
    }
    if (request.pageSize !== undefined) {
      cleanedRequest.pageSize = request.pageSize;
    }
    if (request.sortColumn !== undefined) {
      cleanedRequest.sortColumn = request.sortColumn;
    }
    if (request.sortDirection !== undefined) {
      cleanedRequest.sortDirection = request.sortDirection;
    }

    // Only include optional properties that have actual values
    for (const [key, value] of Object.entries(request)) {
      // Skip required properties (already handled above)
      if ([fieldNames.pa, 'pageSize', 'sortColumn', 'sortDirection'].includes(key)) {
        continue;
      }

      if (value !== null && value !== undefined && value !== '') {
        // Special handling for different types
        if (typeof value === 'number' || typeof value === 'boolean') {
          cleanedRequest[key] = value;
        } else if (value instanceof Date) {
          // Format dates to ISO string for API
          cleanedRequest[key] = value.toISOString();
        } else if (typeof value === 'string' && value.trim() !== '') {
          cleanedRequest[key] = value;
        }
      }
    }

    return cleanedRequest as Partial<IGetPackagesRequest>;
  }
}
