// ============================================================================
// PACKAGES LIST COMPONENT - Displays and manages package data in a data grid
// ============================================================================

import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject,
  OnDestroy,
  signal,
  computed,
  ViewChild
} from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { takeUntil, combineLatest } from 'rxjs';
import { toObservable } from '@angular/core/rxjs-interop';
import moment from "moment-timezone";

// === PRIMENG IMPORTS ===
import { TableModule } from 'primeng/table';
import { MultiSelectModule } from 'primeng/multiselect';
import { ButtonModule } from "primeng/button";
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';
import { RippleModule } from 'primeng/ripple';
import { FormsModule } from '@angular/forms';

// === SHARED LIBRARY IMPORTS ===
import {
  IGetPackagesRequest,
  IGetPackagesResponse,
  ISearchPackageDto,
  nameOf,
  IPackageTypeEnum,
  IPackageStatusEnum,
  EnumDropdownOptionsService,
  IEnumDropdownOptions,
  GeneralService,
  IDataGridFields,
  IBasedDataGridRequest,
  FiltersDrawerSidebarComponent,
  IFiltersDrawerConfig,
  AppliedFiltersTagsComponent,
  IAppliedFilterTag,
  BaseDataGridComponent,
  IBaseDataGridConfig,
  IPackage
} from 'SharedModules.Library';

// === COMPONENT IMPORTS ===
import { DataGridHeaderFooterComponent } from '../../../shared/components/data-grid-header-footer/data-grid-header-footer.component';
import { IPackagesFilterActionEvent, IPackagesFilterChangeEvent, PackagesListFiltersComponent } from './packages-list-filters/packages-list-filters.component';

// === SERVICE IMPORTS ===
import { AppliedFiltersAdapterService } from '../../../shared/services/applied-filters-adapter.service';
import { PackageListComponentHelperService } from './package-list-component-helper.service';


// ============================================================================
// COMPONENT DEFINITION
// ============================================================================

/**
 * Packages List Component
 *
 * Displays packages in a data grid with:
 * - Pagination and sorting
 * - Advanced filtering (status, type, dates, teacher, student, etc.)
 * - Applied filters display
 * - Search functionality
 * - Export capabilities
 *
 * Extends BaseDataGridComponent for common data grid functionality
 */
@Component({
  selector: 'app-packages-list',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    MultiSelectModule,
    ButtonModule,
    FormsModule,
    InputTextModule,
    TooltipModule,
    RippleModule,
    DataGridHeaderFooterComponent,
    AppliedFiltersTagsComponent,
    PackagesListFiltersComponent,
    FiltersDrawerSidebarComponent
  ],
  templateUrl: './packages-list.component.html',
  styleUrls: ['./packages-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PackagesListComponent extends BaseDataGridComponent<
  IGetPackagesRequest,
  IGetPackagesResponse
> implements OnInit, OnDestroy {

  private packageHelperService = inject(PackageListComponentHelperService);
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);
  private appliedFiltersAdapter = inject(AppliedFiltersAdapterService);
  protected override generalService = inject(GeneralService);
  protected override router = inject(Router);

  // ============================================================================
  // COMPONENT SIGNALS AND COMPUTED PROPERTIES
  // ============================================================================

  packageTypes = signal<IEnumDropdownOptions[]>([]);
  packageStatuses = signal<IEnumDropdownOptions[]>([]);

  // Computed property for visible columns (excludes hidden columns)
  visibleColumns = computed(() => this.cols.filter((col) => !col.hide));

  // Computed property for packages data
  packagesForTable = computed(() => {
    return this.dataResponse()?.pageData || [];
  });

  // Computed property for filter state
  packagesFilterState = computed(() => ({
    queryParams: this.queryParams(),
    packageTypes: this.packageTypes(),
    packageStatuses: this.packageStatuses(),
    isFilterOpen: this.isFiltersDrawerVisible
  }));

  // Filters drawer configuration
  filtersDrawerConfig: IFiltersDrawerConfig = {
    headerText: 'Packages List Filters',
    headerIcon: 'pi pi-filter',
    position: 'right',
    width: '450px',
    showApplyButton: true,
    showResetButton: true,
    showCloseButton: true,
    applyButtonLabel: 'Apply Filters',
    resetButtonLabel: 'Reset All',
    closeButtonLabel: 'Close',
    applyButtonIcon: 'pi pi-check',
    resetButtonIcon: 'pi pi-refresh',
    closeButtonIcon: 'pi pi-times',
  };

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  override ngOnInit(): void {
    this.initializeComponent();
    this.loadEnumData();

    // Filter out hidden columns for initial selection (following teachers-list pattern)
    const visibleColumns = this.cols.filter(col => !col.hide);
    this.selectedColumns.set(visibleColumns);

    super.ngOnInit(); // Call parent ngOnInit to set up base functionality
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy(); // Call parent ngOnDestroy for cleanup
  }

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATIONS
  // ============================================================================

  override getConfig(): IBaseDataGridConfig<IGetPackagesRequest> {
    return {
      defaultRequest: this.packageHelperService.createDefaultRequest(),
      apiEndpoint: IPackage.getPackages,
      errorPrefix: 'Failed to load packages',
      mapUrlParamsToRequest: (params: Params) =>
        this.packageHelperService.mapQueryParamsToRequest(params),
      createDefaultRequest: () =>
        this.packageHelperService.createDefaultRequest(),
      appliedFiltersConfig: {
        convertToFilterTags: (request, urlParams) => this.convertToFilterTags(request, urlParams),
        getFiltersCount: (filters) => filters.length
      },
      fieldNames: this.packageHelperService.getFieldNames(),
    };
  }

  // ============================================================================
  // INITIALIZATION METHODS
  // ============================================================================

  private initializeComponent(): void {
    // Initialize table columns
    this.cols = this.packageHelperService.initializeTableColumns();
  }

  private loadEnumData(): void {
    // Load package types manually
    this.packageTypes.set([
      { value: IPackageTypeEnum.FreeTrial, label: 'Free Trial' },
      { value: IPackageTypeEnum.Paid, label: 'Paid' },
      { value: IPackageTypeEnum.Gift, label: 'Gift' }
    ]);

    // Load package statuses manually
    this.packageStatuses.set([
      { value: IPackageStatusEnum.Active, label: 'Active' },
      { value: IPackageStatusEnum.InActive, label: 'Inactive' },
      { value: IPackageStatusEnum.Expired, label: 'Expired' },
      { value: IPackageStatusEnum.Completed, label: 'Completed' },
      { value: IPackageStatusEnum.Refunded, label: 'Refunded' }
    ]);
  }

  // ============================================================================
  // APPLIED FILTERS METHODS
  // ============================================================================

  private convertToFilterTags(request: IGetPackagesRequest, _urlParams: any): IAppliedFilterTag[] {
    const filters: IAppliedFilterTag[] = [];
    const fieldNames = nameOf<IGetPackagesRequest>();

    // Search term filter
    if (request.searchTerm) {
      filters.push({
        id: fieldNames['searchTerm']!,
        label: `Search: ${request.searchTerm}`,
        icon: 'pi pi-search',
        type: 'search',
        removeData: { filterName: fieldNames['searchTerm']! }
      });
    }

    // Package type filter
    if (request.packageType !== null && request.packageType !== undefined) {
      const typeText = this.getPackageTypeText(request.packageType);
      filters.push({
        id: fieldNames['packageType']!,
        label: `Type: ${typeText}`,
        icon: 'pi pi-tag',
        type: 'select',
        removeData: { filterName: fieldNames['packageType']! }
      });
    }

    // Package status filter
    if (request.packageStatus !== null && request.packageStatus !== undefined) {
      const statusText = this.getPackageStatusText(request.packageStatus);
      filters.push({
        id: fieldNames['packageStatus']!,
        label: `Status: ${statusText}`,
        icon: 'pi pi-info-circle',
        type: 'select',
        removeData: { filterName: fieldNames['packageStatus']! }
      });
    }

    // Date filters
    if (request.purchasedFrom) {
      filters.push({
        id: fieldNames['purchasedFrom']!,
        label: `Purchased From: ${this.formatDate(request.purchasedFrom)}`,
        icon: 'pi pi-calendar',
        type: 'date',
        removeData: { filterName: fieldNames['purchasedFrom']! }
      });
    }

    if (request.purchasedTo) {
      filters.push({
        id: fieldNames['purchasedTo']!,
        label: `Purchased To: ${this.formatDate(request.purchasedTo)}`,
        icon: 'pi pi-calendar',
        type: 'date',
        removeData: { filterName: fieldNames['purchasedTo']! }
      });
    }

    // Teacher filter
    if (request.teacherId) {
      filters.push({
        id: fieldNames['teacherId']!,
        label: `Teacher: ${request.teacherId}`, // TODO: Resolve teacher name
        icon: 'pi pi-user',
        type: 'select',
        removeData: { filterName: fieldNames['teacherId']! }
      });
    }

    // Student filter
    if (request.studentId) {
      filters.push({
        id: fieldNames['studentId']!,
        label: `Student: ${request.studentId}`, // TODO: Resolve student name
        icon: 'pi pi-graduation-cap',
        type: 'select',
        removeData: { filterName: fieldNames['studentId']! }
      });
    }

    return filters;
  }

  // ============================================================================
  // FILTER METHODS
  // ============================================================================

  onFiltersChange(event: IPackagesFilterChangeEvent): void {
    this.updateUrlParamsWithFilters(event.filters);
  }

  onFiltersAction(event: IPackagesFilterActionEvent): void {
    if (event.action === 'search') {
      this.updateUrlParamsWithFilters(event.filters);
    } else if (event.action === 'reset') {
      this.resetFilters();
    }
  }

  override resetFilters(): void {
    const defaultRequest = this.packageHelperService.createDefaultRequest();
    this.updateUrlParamsWithFilters({
      pageNumber: defaultRequest.pageNumber,
      pageSize: defaultRequest.pageSize,
      sortColumn: defaultRequest.sortColumn,
      sortDirection: defaultRequest.sortDirection
    });
  }

  override removeFilter(filterName: string, event?: MouseEvent): void {
    if (event) event.stopPropagation();

    const currentParams = this.queryParams();
    const updatedParams = { ...currentParams };

    // Remove the specific filter
    delete updatedParams[filterName as keyof IGetPackagesRequest];

    // Reset to first page when removing filters
    updatedParams.pageNumber = 1;

    this.updateUrlParamsWithFilters(updatedParams);
  }

  private updateUrlParamsWithFilters(params: Partial<IGetPackagesRequest>): void {
    const currentParams = this.queryParams();
    const updatedParams = { ...currentParams, ...params };
    this.queryParams.set(updatedParams);

    // Update URL without triggering navigation
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: updatedParams,
      queryParamsHandling: 'merge'
    });
  }

  // ============================================================================
  // APPLIED FILTERS CONFIGURATION
  // ============================================================================

  private getAppliedFiltersConfig() {
    const fieldNames = nameOf<IGetPackagesRequest>();
    
    return {
      [fieldNames.searchTerm!]: {
        label: 'Search',
        type: 'text' as const
      },
      [fieldNames.packageType!]: {
        label: 'Package Type',
        type: 'enum' as const,
        enumOptions: this.packageTypes()
      },
      [fieldNames.packageStatus!]: {
        label: 'Package Status',
        type: 'enum' as const,
        enumOptions: this.packageStatuses()
      },
      [fieldNames.teacherId!]: {
        label: 'Teacher',
        type: 'teacher' as const
      },
      [fieldNames.studentId!]: {
        label: 'Student',
        type: 'student' as const
      },
      [fieldNames.purchasedFrom!]: {
        label: 'Purchased From',
        type: 'date' as const
      },
      [fieldNames.purchasedTo!]: {
        label: 'Purchased To',
        type: 'date' as const
      },
      [fieldNames.expiresFrom!]: {
        label: 'Expires From',
        type: 'date' as const
      },
      [fieldNames.expiresTo!]: {
        label: 'Expires To',
        type: 'date' as const
      }
    };
  }

  // ============================================================================
  // TABLE METHODS
  // ============================================================================

  onExport(): void {
    if (this.table && this.dataResponse() && this.selectedColumns()) {
      this.packageHelperService.exportTable(this.table, this.selectedColumns(), this.dataResponse()!);
    }
  }

  /**
   * Handle column selection changes
   */
  override onColumnsChange(selectedColumns: IDataGridFields[]): void {
    this.selectedColumns.set(selectedColumns);
  }

  /**
   * Handle column reorder event
   */
  onColumnReorder(event: { columns?: IDataGridFields[] }): void {
    if (event.columns?.length) {
      const reorderedColumns = event.columns.filter(
        (col: IDataGridFields) => col?.field && col?.header
      ) as IDataGridFields[];
      this.selectedColumns.set([...reorderedColumns]);
    }
  }

  /**
   * Update search term from input event
   */
  override updateSearchTerm(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onSearchChange(target.value);
  }

  /**
   * Clear search term
   */
  override clearSearchTerm(): void {
    this.onSearchChange('');
  }

  /**
   * Handle search input changes
   */
  override onSearchChange(searchTerm: string): void {
    const currentParams = { ...this.queryParams() };
    currentParams.searchTerm = searchTerm || null;
    currentParams.pageNumber = 1; // Reset to first page when searching

    this.queryParams.set(currentParams);
    this.updateUrlParams();
  }

  /**
   * Export packages to Excel
   */
  exportToExcel(): void {
    const response = this.dataResponse();
    if (response) {
      this.packageHelperService.exportTable(
        this.table,
        this.selectedColumns(),
        response
      );
    }
  }



  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  protected getEntityDisplayName(): string {
    return 'Packages';
  }

  protected getCreateRoute(): string {
    return '/admin/packages/create';
  }

  /**
   * Get display name for sort column
   */
  getSortColumnDisplayName(): string {
    const sortColumn = this.queryParams().sortColumn;
    const column = this.cols.find(col => col.field === sortColumn);
    return column ? column.header : sortColumn || 'Unknown';
  }

  /**
   * Get display name for sort column from URL parameters
   */
  getSortColumnDisplayNameFromUrl(): string {
    const fieldNames = nameOf<IGetPackagesRequest>();
    const sortColumn = this.currentUrlParams()[fieldNames.sortColumn!];
    const column = this.cols.find(col => col.field === sortColumn);
    return column ? column.header : sortColumn || 'Unknown';
  }

  // ============================================================================
  // TEMPLATE HELPER METHODS
  // ============================================================================

  getPackageTypeText(packageType: IPackageTypeEnum): string {
    return this.generalService.getEnumDisplayText(packageType, IPackageTypeEnum);
  }

  getPackageStatusText(packageStatus: IPackageStatusEnum): string {
    return this.generalService.getEnumDisplayText(packageStatus, IPackageStatusEnum);
  }

  getStudentNames(students: any[]): string {
    if (!students || students.length === 0) return '';
    return students.map(s => `${s.firstName} ${s.lastName}`).join(', ');
  }

  getExpirationClass(expirationDate: Date | string | null): string {
    if (!expirationDate) return '';

    const expDate = moment(expirationDate);
    const now = moment();
    const daysUntilExpiration = expDate.diff(now, 'days');

    if (daysUntilExpiration < 0) {
      return 'text-red-500'; // Expired
    } else if (daysUntilExpiration <= 7) {
      return 'text-orange-500'; // Expiring soon
    } else if (daysUntilExpiration <= 30) {
      return 'text-yellow-500'; // Expiring within a month
    }
    return 'text-green-500'; // Good expiration date
  }

  isExpiringSoon(expirationDate: Date | string | null): boolean {
    if (!expirationDate) return false;

    const expDate = moment(expirationDate);
    const now = moment();
    const daysUntilExpiration = expDate.diff(now, 'days');

    return daysUntilExpiration >= 0 && daysUntilExpiration <= 7;
  }

  getLessonsProgressPercentage(packageItem: ISearchPackageDto): number {
    if (packageItem.totalLessons === 0) return 0;
    const completed = packageItem.totalLessons - packageItem.remainingLessons;
    return Math.round((completed / packageItem.totalLessons) * 100);
  }

  formatDate(date: Date | string | null): string {
    if (!date) return '';
    try {
      return moment(date).format('DD/MM/YYYY');
    } catch {
      return '';
    }
  }



  getFieldValue(item: any, field: string): string {
    const value = item[field];
    if (value === null || value === undefined) return '';
    if (typeof value === 'object') return JSON.stringify(value);
    return String(value);
  }

  hasActiveFilters(): boolean {
    return this.appliedFilters().length > 0;
  }



  onCreateClick(): void {
    this.router.navigate([this.getCreateRoute()]);
  }
}
