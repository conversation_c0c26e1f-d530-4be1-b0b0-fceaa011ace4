// ============================================================================
// PACKAGES LIST COMPONENT - Displays and manages package data in a data grid
// ============================================================================

import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject,
  OnDestroy,
  signal,
  computed,
  ViewChild
} from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { takeUntil, combineLatest } from 'rxjs';
import { toObservable } from '@angular/core/rxjs-interop';
import moment from "moment-timezone";

// === PRIMENG IMPORTS ===
import { TableModule } from 'primeng/table';
import { MultiSelectModule } from 'primeng/multiselect';
import { ButtonModule } from "primeng/button";
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';
import { RippleModule } from 'primeng/ripple';
import { FormsModule } from '@angular/forms';

// === SHARED LIBRARY IMPORTS ===
import {
  IGetPackagesRequest,
  IGetPackagesResponse,
  ISearchPackageDto,
  nameOf,
  IPackageTypeEnum,
  IPackageStatusEnum,
  EnumDropdownOptionsService,
  IEnumDropdownOptions,
  GeneralService,
  IDataGridFields,
  IBasedDataGridRequest,
  FiltersDrawerSidebarComponent,
  IFiltersDrawerConfig,
  AppliedFiltersTagsComponent
} from 'SharedModules.Library';

// === COMPONENT IMPORTS ===
import { DataGridHeaderFooterComponent } from '../../../shared/components/data-grid-header-footer/data-grid-header-footer.component';
import { IPackagesFilterActionEvent, IPackagesFilterChangeEvent, PackagesListFiltersComponent } from './packages-list-filters/packages-list-filters.component';

// === SERVICE IMPORTS ===
import { AppliedFiltersAdapterService } from '../../../shared/services/applied-filters-adapter.service';
import { PackageListComponentHelperService } from './package-list-component-helper.service';

// === BASE COMPONENT ===
import { BaseDataGridComponent, IBaseDataGridConfig } from 'SharedModules.Library';

// ============================================================================
// COMPONENT DEFINITION
// ============================================================================

/**
 * Packages List Component
 *
 * Displays packages in a data grid with:
 * - Pagination and sorting
 * - Advanced filtering (status, type, dates, teacher, student, etc.)
 * - Applied filters display
 * - Search functionality
 * - Export capabilities
 *
 * Extends BaseDataGridComponent for common data grid functionality
 */
@Component({
  selector: 'app-packages-list',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    MultiSelectModule,
    ButtonModule,
    FormsModule,
    InputTextModule,
    TooltipModule,
    RippleModule,
    DataGridHeaderFooterComponent,
    AppliedFiltersTagsComponent,
    PackagesListFiltersComponent,
    FiltersDrawerSidebarComponent
  ],
  templateUrl: './packages-list.component.html',
  styleUrls: ['./packages-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PackagesListComponent extends BaseDataGridComponent<
  IGetPackagesRequest,
  IGetPackagesResponse
> implements OnInit, OnDestroy {

  private packageHelperService = inject(PackageListComponentHelperService);
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);
  private appliedFiltersAdapter = inject(AppliedFiltersAdapterService);
  protected override generalService = inject(GeneralService);
  protected override router = inject(Router);

  // ============================================================================
  // COMPONENT SIGNALS AND COMPUTED PROPERTIES
  // ============================================================================

  packageTypes = signal<IEnumDropdownOptions[]>([]);
  packageStatuses = signal<IEnumDropdownOptions[]>([]);

  // Applied filters computed properties
  appliedFilters = computed(() => {
    const params = this.queryParams();
    return this.appliedFiltersAdapter.getAppliedFilters(params, this.getAppliedFiltersConfig());
  });

  // Filters drawer configuration
  filtersDrawerConfig = computed((): IFiltersDrawerConfig => ({
    showFilters: this.showFilters(),
    filtersCount: this.appliedFilters().length
  }));

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  ngOnInit(): void {
    this.initializeComponent();
    this.loadEnumData();
    this.setupDataSubscription();
  }

  ngOnDestroy(): void {
    this.cleanup();
  }

  // ============================================================================
  // INITIALIZATION METHODS
  // ============================================================================

  private initializeComponent(): void {
    // Initialize base data grid configuration
    const config: IBaseDataGridConfig<IGetPackagesRequest, IGetPackagesResponse> = {
      defaultRequest: this.packageHelperService.createDefaultRequest(),
      helperService: this.packageHelperService,
      entityName: 'packages'
    };

    this.initialize(config);
  }

  private loadEnumData(): void {
    // Load package types
    this.packageTypes.set(
      this.enumDropdownOptionsService.getEnumDropdownOptions(IPackageTypeEnum)
    );

    // Load package statuses
    this.packageStatuses.set(
      this.enumDropdownOptionsService.getEnumDropdownOptions(IPackageStatusEnum)
    );
  }

  private setupDataSubscription(): void {
    // Subscribe to query parameter changes and load data
    combineLatest([
      this.route.queryParams,
      toObservable(this.refreshTrigger)
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe(([params]) => {
      this.handleQueryParamsChange(params);
    });
  }

  // ============================================================================
  // DATA LOADING METHODS
  // ============================================================================

  protected override loadData(request: IGetPackagesRequest): void {
    this.setLoading(true);
    
    // TODO: Replace with actual packages API service call
    // this.packagesService.getPackages(request)
    //   .pipe(takeUntil(this.destroy$))
    //   .subscribe({
    //     next: (response) => {
    //       this.handleDataResponse(response);
    //       this.setLoading(false);
    //     },
    //     error: (error) => {
    //       this.handleError(error);
    //       this.setLoading(false);
    //     }
    //   });

    // Temporary mock data for development
    setTimeout(() => {
      const mockResponse: IGetPackagesResponse = {
        pageData: [],
        currentPage: request.pageNumber,
        pageSize: request.pageSize,
        totalRecords: 0,
        totalPages: 0,
        sortColumn: request.sortColumn || '',
        sortDirection: request.sortDirection || 'asc'
      };
      this.handleDataResponse(mockResponse);
      this.setLoading(false);
    }, 500);
  }

  // ============================================================================
  // FILTER METHODS
  // ============================================================================

  onFiltersChange(event: IPackagesFilterChangeEvent): void {
    this.updateQueryParams(event.filters);
  }

  onFiltersAction(event: IPackagesFilterActionEvent): void {
    if (event.action === 'apply') {
      this.updateQueryParams(event.filters);
    } else if (event.action === 'reset') {
      this.resetFilters();
    }
  }

  private resetFilters(): void {
    const defaultRequest = this.packageHelperService.createDefaultRequest();
    this.updateQueryParams({
      pageNumber: defaultRequest.pageNumber,
      pageSize: defaultRequest.pageSize,
      sortColumn: defaultRequest.sortColumn,
      sortDirection: defaultRequest.sortDirection
    });
  }

  // ============================================================================
  // APPLIED FILTERS CONFIGURATION
  // ============================================================================

  private getAppliedFiltersConfig() {
    const fieldNames = nameOf<IGetPackagesRequest>();
    
    return {
      [fieldNames.searchTerm!]: {
        label: 'Search',
        type: 'text' as const
      },
      [fieldNames.packageType!]: {
        label: 'Package Type',
        type: 'enum' as const,
        enumOptions: this.packageTypes()
      },
      [fieldNames.packageStatus!]: {
        label: 'Package Status',
        type: 'enum' as const,
        enumOptions: this.packageStatuses()
      },
      [fieldNames.teacherId!]: {
        label: 'Teacher',
        type: 'teacher' as const
      },
      [fieldNames.studentId!]: {
        label: 'Student',
        type: 'student' as const
      },
      [fieldNames.purchasedFrom!]: {
        label: 'Purchased From',
        type: 'date' as const
      },
      [fieldNames.purchasedTo!]: {
        label: 'Purchased To',
        type: 'date' as const
      },
      [fieldNames.expiresFrom!]: {
        label: 'Expires From',
        type: 'date' as const
      },
      [fieldNames.expiresTo!]: {
        label: 'Expires To',
        type: 'date' as const
      }
    };
  }

  // ============================================================================
  // TABLE METHODS
  // ============================================================================

  onExport(): void {
    if (this.table && this.response() && this.cols()) {
      this.packageHelperService.exportTable(this.table, this.cols(), this.response()!);
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  protected override getEntityDisplayName(): string {
    return 'Packages';
  }

  protected override getCreateRoute(): string {
    return '/admin/packages/create';
  }

  // ============================================================================
  // TEMPLATE HELPER METHODS
  // ============================================================================

  getPackageTypeText(packageType: IPackageTypeEnum): string {
    return this.generalService.getEnumDisplayText(packageType, IPackageTypeEnum);
  }

  getPackageStatusText(packageStatus: IPackageStatusEnum): string {
    return this.generalService.getEnumDisplayText(packageStatus, IPackageStatusEnum);
  }

  getStudentNames(students: any[]): string {
    if (!students || students.length === 0) return '';
    return students.map(s => `${s.firstName} ${s.lastName}`).join(', ');
  }

  getLessonsProgressPercentage(packageItem: ISearchPackageDto): number {
    if (packageItem.totalLessons === 0) return 0;
    const completed = packageItem.totalLessons - packageItem.remainingLessons;
    return Math.round((completed / packageItem.totalLessons) * 100);
  }

  formatDate(date: Date | string | null): string {
    if (!date) return '';
    try {
      return moment(date).format('DD/MM/YYYY');
    } catch {
      return '';
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  getExpirationClass(expirationDate: Date | string): string {
    if (!expirationDate) return '';
    const expDate = moment(expirationDate);
    const now = moment();
    const daysUntilExpiration = expDate.diff(now, 'days');

    if (daysUntilExpiration < 0) return 'expired';
    if (daysUntilExpiration <= 14) return 'expiring-soon';
    return '';
  }

  isExpiringSoon(expirationDate: Date | string): boolean {
    if (!expirationDate) return false;
    const expDate = moment(expirationDate);
    const now = moment();
    const daysUntilExpiration = expDate.diff(now, 'days');
    return daysUntilExpiration <= 14 && daysUntilExpiration >= 0;
  }

  getFieldValue(item: any, field: string): string {
    const value = item[field];
    if (value === null || value === undefined) return '';
    if (typeof value === 'object') return JSON.stringify(value);
    return String(value);
  }

  hasActiveFilters(): boolean {
    return this.appliedFilters().length > 0;
  }

  // ============================================================================
  // ACTION METHODS
  // ============================================================================

  onViewPackage(packageItem: ISearchPackageDto): void {
    this.router.navigate(['/admin/packages', packageItem.id]);
  }

  onEditPackage(packageItem: ISearchPackageDto): void {
    this.router.navigate(['/admin/packages', packageItem.id, 'edit']);
  }

  onCreateClick(): void {
    this.router.navigate([this.getCreateRoute()]);
  }
}
