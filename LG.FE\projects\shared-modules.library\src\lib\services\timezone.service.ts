import { Injectable } from '@angular/core';
import moment from 'moment-timezone';

export type TimeUnit = moment.unitOfTime.DurationConstructor;
export type StartOfUnit = moment.unitOfTime.StartOf;
export type DateInput = Date | string;

/**
 * Service for handling timezone-related operations and date conversions
 */
@Injectable({
  providedIn: 'root'
})
export class TimezoneService {
  private readonly DEFAULT_FORMAT = 'YYYY-MM-DD HH:mm:ss';
  private readonly UTC = 'UTC';
  private currentTimezone: string;

  constructor() {
    this.currentTimezone = moment.tz.guess() || this.UTC;
  }

  /**
   * Sets the current timezone for the service
   * @param timezone - The timezone identifier (e.g., 'America/New_York')
   * @throws Error if timezone is invalid
   */
  setTimezone(timezone: string): void {
    if (!this.isValidTimezone(timezone)) {
      throw new Error(`Invalid timezone: ${timezone}`);
    }
    this.currentTimezone = timezone;
  }

  /**
   * Gets the current timezone setting
   * @returns The current timezone identifier
   */
  getTimezone(): string {
    return this.currentTimezone;
  }

  /**
   * Gets a list of all available timezone names
   * @returns Array of timezone identifiers
   */
  getAvailableTimezones(): string[] {
    return moment.tz.names();
  }

  getDateInUserTimezone(date: Date | null = null, timezone?: string): moment.Moment {
    return moment.tz(date || new Date(), timezone || this.currentTimezone);
  }

  /**
   * Converts a UTC date to local time in the specified timezone
   * @param utcDate - The UTC date to convert
   * @param targetTimezone - Optional target timezone (uses current timezone if not specified)
   * @returns Date object in the target timezone
   */
  convertToLocalTime(utcDate: DateInput, targetTimezone?: string): Date {
    const timezone = this.validateTimezone(targetTimezone);
    return moment.utc(utcDate).tz(timezone).toDate();
  }

  /**
   * Converts a UTC date to local timezone with flexible output format
   * @param utcDate - The UTC date to convert (string or Date object)
   * @param options - Configuration options
   * @param options.timezone - Target timezone (defaults to current user timezone)
   * @param options.outputFormat - Output format ('string', 'date', or 'moment', defaults to 'string')
   * @param options.formatPattern - Format pattern for string output (defaults to DEFAULT_FORMAT)
   * @param options.dayBoundary - Optional day boundary adjustment ('start', 'end', or undefined for no adjustment)
   * @returns Formatted string, Date object, or Moment object in the target timezone
   */
  convertUtcToLocal<T extends 'string' | 'date' | 'moment'>(
    utcDate: DateInput,
    options: {
      timezone?: string;
      outputFormat?: T;
      formatPattern?: string;
      dayBoundary?: 'start' | 'end';
    } = {}
  ): T extends 'string' ? string : T extends 'date' ? Date : moment.Moment {
    const {
      timezone = this.getTimezone(),
      outputFormat = 'string' as T,
      formatPattern = this.DEFAULT_FORMAT,
      dayBoundary
    } = options;

    const tz = this.validateTimezone(timezone);
    let momentDate = moment.utc(utcDate).tz(tz);

    // Apply day boundary adjustment if specified
    if (dayBoundary === 'start') {
      momentDate = momentDate.startOf('day');
    } else if (dayBoundary === 'end') {
      momentDate = momentDate.endOf('day');
    }

    if (outputFormat === 'date') {
      return momentDate.toDate() as any;
    } else if (outputFormat === 'moment') {
      return momentDate as any;
    }

    return momentDate.format(formatPattern) as any;
  }


  /**
   * Converts a local date to UTC with flexible output format
   * @param localDate - The local date to convert (string or Date object)
   * @param options - Configuration options
   * @param options.sourceTimezone - Source timezone (defaults to current user timezone)
   * @param options.outputFormat - Output format ('string', 'date', or 'moment', defaults to 'string')
   * @param options.formatPattern - Format pattern for string output (defaults to DEFAULT_FORMAT)
   * @param options.dayBoundary - Optional day boundary adjustment ('start', 'end', or undefined for no adjustment)
   * @returns Formatted string, Date object, or Moment object in UTC
   */
  convertLocalToUtc<T extends 'string' | 'date' | 'moment'>(
    localDate: DateInput,
    options: {
      sourceTimezone?: string;
      outputFormat?: T;
      formatPattern?: string;
      dayBoundary?: 'start' | 'end';
    } = {}
  ): T extends 'string' ? string : T extends 'date' ? Date : moment.Moment {
    const {
      sourceTimezone = this.getTimezone(),
      outputFormat = 'string' as T,
      formatPattern = this.DEFAULT_FORMAT,
      dayBoundary
    } = options;

    const tz = this.validateTimezone(sourceTimezone);
    let momentDate = moment.tz(localDate, tz);
    
    // Apply day boundary adjustment if specified
    if (dayBoundary === 'start') {
      momentDate = momentDate.startOf('day');
    } else if (dayBoundary === 'end') {
      momentDate = momentDate.endOf('day');
    }
    
    // Convert to UTC after any adjustments
    momentDate = momentDate.utc();

    if (outputFormat === 'date') {
      return momentDate.toDate() as any;
    } else if (outputFormat === 'moment') {
      return momentDate as any;
    }

    return momentDate.format(formatPattern) as any;
  }

  /**
   * Converts a local date to UTC
   * @param localDate - The local date to convert
   * @param sourceTimezone - Optional source timezone (uses current timezone if not specified)
   * @returns Date object in UTC
   */
  convertToUTC(localDate: DateInput, sourceTimezone?: string): Date {
    const timezone = this.validateTimezone(sourceTimezone);
    return moment.tz(localDate, timezone).utc().toDate();
  }

  /**
   * Converts a date from one timezone to another
   * @param date - The date to convert
   * @param sourceTimezone - The source timezone
   * @param targetTimezone - The target timezone
   * @returns Date object in the target timezone
   * @throws Error if either timezone is invalid
   */
  convertBetweenTimezones(date: DateInput, sourceTimezone: string, targetTimezone: string): Date {
    if (!this.isValidTimezone(sourceTimezone) || !this.isValidTimezone(targetTimezone)) {
      throw new Error('Invalid source or target timezone');
    }
    return moment.tz(date, sourceTimezone).tz(targetTimezone).toDate();
  }

  /**
 * Gets the current time in user-friendly format for a specific timezone
 * @param timezone - The timezone identifier (e.g., 'America/New_York')
 * @param showDate - Optional flag to include date in the output (defaults to false)
 * @returns Formatted time string (HH:mm) or time with date (HH:mm - D MMM YYYY)
 */
  formatCurrentTime(timezone: string, showDate: boolean = false): string {
    const tz = this.validateTimezone(timezone);
    const currentTime = moment().tz(tz);

    if (showDate) {
      return currentTime.format('HH:mm - D MMM YYYY');
    }
    return currentTime.format('HH:mm');
  }

  /**
   * Formats a date in the specified timezone
   * @param date - The date to format
   * @param formatPattern - Optional format pattern (defaults to YYYY-MM-DD HH:mm:ss)
   * @param timezone - Optional timezone (uses current timezone if not specified)
   * @returns Formatted date string
   */
  formatInTimezone(date: DateInput, formatPattern: string = this.DEFAULT_FORMAT, timezone?: string): string {
    return moment.utc(date).tz(timezone!).format(formatPattern);
  }

  /**
   * Gets the current time in the specified timezone
   * @param timezone - Optional timezone (uses current timezone if not specified)
   * @returns Current date and time in the specified timezone
   */
  getCurrentTimeInZone(timezone?: string): Date {
    const tz = this.validateTimezone(timezone);
    return moment().tz(tz).toDate();
  }

  /**
   * Checks if a timezone identifier is valid
   * @param timezone - The timezone identifier to check
   * @returns boolean indicating if the timezone is valid
   */
  isValidTimezone(timezone: string): boolean {
    return moment.tz.zone(timezone) !== null;
  }

  /**
   * Gets the offset in minutes for a timezone
   * @param timezone - Optional timezone (uses current timezone if not specified)
   * @returns Offset in minutes from UTC
   */
  getOffsetMinutes(timezone?: string): number {
    const tz = this.validateTimezone(timezone);
    return moment.tz(tz).utcOffset();
  }

  /**
   * Gets the abbreviation for a timezone (e.g., EST, PST)
   * @param timezone - Optional timezone (uses current timezone if not specified)
   * @returns Timezone abbreviation
   */
  getZoneAbbreviation(timezone?: string): string {
    const tz = this.validateTimezone(timezone);
    return moment.tz(tz).format('z');
  }

  /**
   * Checks if a date is in daylight saving time
   * @param date - The date to check
   * @param timezone - Optional timezone (uses current timezone if not specified)
   * @returns boolean indicating if the date is in DST
   */
  isDaylightSavingTime(date: DateInput, timezone?: string): boolean {
    const tz = this.validateTimezone(timezone);
    return moment.tz(date, tz).isDST();
  }

  /**
   * Adds a duration to a date
   * @param date - The base date
   * @param amount - The amount to add
   * @param unit - The unit of time to add (e.g., 'days', 'hours')
   * @param timezone - Optional timezone (uses current timezone if not specified)
   * @returns New date with duration added
   */
  addDuration(date: DateInput, amount: number, unit: TimeUnit, timezone?: string): Date {
    const tz = this.validateTimezone(timezone);
    return moment.tz(date, tz).add(amount, unit).toDate();
  }

  /**
   * Subtracts a duration from a date
   * @param date - The base date
   * @param amount - The amount to subtract
   * @param unit - The unit of time to subtract (e.g., 'days', 'hours')
   * @param timezone - Optional timezone (uses current timezone if not specified)
   * @returns New date with duration subtracted
   */
  subtractDuration(date: DateInput, amount: number, unit: TimeUnit, timezone?: string): Date {
    const tz = this.validateTimezone(timezone);
    return moment.tz(date, tz).subtract(amount, unit).toDate();
  }

  /**
   * Gets the start of a unit of time (e.g., start of day, start of month)
   * @param date - The base date
   * @param unit - The unit to get the start of (e.g., 'day', 'month')
   * @param timezone - Optional timezone (uses current timezone if not specified)
   * @returns Date at the start of the specified unit
   */
  getStartOf(date: DateInput, unit: StartOfUnit, timezone?: string): Date {
    const tz = this.validateTimezone(timezone);
    return moment.tz(date, tz).startOf(unit).toDate();
  }

  /**
   * Gets the end of a unit of time (e.g., end of day, end of month)
   * @param date - The base date
   * @param unit - The unit to get the end of (e.g., 'day', 'month')
   * @param timezone - Optional timezone (uses current timezone if not specified)
   * @returns Date at the end of the specified unit
   */
  getEndOf(date: DateInput, unit: StartOfUnit, timezone?: string): Date {
    const tz = this.validateTimezone(timezone);
    return moment.tz(date, tz).endOf(unit).toDate();
  }

  /**
   * Gets today's date at start of day in the current timezone
   * @returns Today's date at 00:00:00
   */
  getToday(): Date {
    return this.getStartOf(
      this.getCurrentTimeInZone(this.getTimezone()),
      'day',
      this.getTimezone()
    );
  }

  /**
   * Validates a timezone identifier
   * @param timezone - Optional timezone (uses current timezone if not specified)
   * @returns Valid timezone identifier
   * @throws Error if timezone is invalid
   */
  private validateTimezone(timezone?: string): string {
    const tz = timezone || this.currentTimezone;
    if (!this.isValidTimezone(tz)) {
      throw new Error(`Invalid timezone: ${tz}`);
    }
    return tz;
  }

  formatTimeRangeDurationInTimezone(start: Date, end: Date): string {
    const timezone = this.getTimezone();
    const startTime = this.formatInTimezone(start, 'HH:mm', timezone);
    const endTime = this.formatInTimezone(end, 'HH:mm', timezone);
    const durationInMinutes = moment(end).diff(moment(start), 'minutes');
    return `${durationInMinutes}'`;
  }
}