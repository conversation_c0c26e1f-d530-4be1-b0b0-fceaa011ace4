// ============================================================================
// PACKAGE LIST COMPONENT HELPER SERVICE
// ============================================================================

import { Injectable, inject } from '@angular/core';
import { Table } from 'primeng/table';
import { Params } from '@angular/router';
import {
  IGetPackagesRequest,
  IGetPackagesResponse,
  ISearchPackageDto,
  IDataGridFields,
  nameOf,
  EnumDropdownOptionsService,
  IPackageTypeEnum,
  IPackageStatusEnum,
  IEnumDropdownOptions
} from 'SharedModules.Library';
import moment from 'moment-timezone';
import { BaseListHelperService } from '../../../shared/services/base-list-helper.service';

const ISearchPackageDtoParamsMap = nameOf<ISearchPackageDto>();

/**
 * Helper service for packages list component
 * Provides utilities for request mapping, defaults, column configuration, and export functionality
 * Following the same pattern as TeacherListComponentHelperService and GroupsListComponentHelperService
 */
@Injectable({
  providedIn: 'root'
})
export class PackageListComponentHelperService extends BaseListHelperService<
  IGetPackagesRequest,
  IGetPackagesResponse,
  ISearchPackageDto
> {
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);

  // Default sort configuration - centralized in one place
  public static readonly DEFAULT_SORT_COLUMN = ISearchPackageDtoParamsMap.dateCreatedUtc!;
  public static readonly DEFAULT_SORT_DIRECTION = 'desc';
  public static readonly DEFAULT_PAGE_SIZE = 10;

  constructor() {
    super();
  }

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATIONS - Required by BaseListHelperService
  // ============================================================================

  getDefaultSortColumn(): string {
    return PackageListComponentHelperService.DEFAULT_SORT_COLUMN;
  }

  getDefaultSortDirection(): 'asc' | 'desc' {
    return PackageListComponentHelperService.DEFAULT_SORT_DIRECTION as 'asc' | 'desc';
  }

  getDefaultPageSize(): number {
    return PackageListComponentHelperService.DEFAULT_PAGE_SIZE;
  }

  getEntityName(): string {
    return 'packages';
  }

  getFieldNames(): Record<string, string> {
    return nameOf<IGetPackagesRequest>();
  }

  createDefaultRequest(): IGetPackagesRequest {
    return {
      pageNumber: 1,
      pageSize: this.getDefaultPageSize(),
      sortColumn: this.getDefaultSortColumn(),
      sortDirection: this.getDefaultSortDirection(),
      searchTerm: null,
      packageType: null,
      packageStatus: null,
      purchasedFrom: null,
      purchasedTo: null,
      expiresFrom: null,
      expiresTo: null,
      teacherId: null,
      parentId: null,
      studentId: null,
      groupId: null,
      teachingLanguageId: null,
      hasAddOnExtension: null,
      parentAccountDeleted: null,
      hasOutstandingTeacherPayments: false
    };
  }

  initializeTableColumns(): IDataGridFields[] {
    const packageFields = nameOf<ISearchPackageDto>();

    return [
      { field: packageFields.id, header: 'Package ID', sortable: true, maxWidth: '120px' },
      { field: packageFields.groupName!, header: 'Group/Package', sortable: true, maxWidth: '200px' },
      { field: packageFields.packageType, header: 'Type', sortable: true, maxWidth: '100px' },
      { field: packageFields.packageStatus, header: 'Status', sortable: true, maxWidth: '100px' },
      { field: packageFields.numberOfStudents, header: 'Students', sortable: true, maxWidth: '120px' },
      { field: 'teacher', header: 'Teacher', sortable: false, maxWidth: '150px' },
      { field: packageFields.totalLessons, header: 'Total Lessons', sortable: true, maxWidth: '120px' },
      { field: packageFields.remainingLessons, header: 'Remaining', sortable: true, maxWidth: '120px' },
      { field: packageFields.expiresOnDateUtc!, header: 'Expires On', sortable: true, maxWidth: '120px' },
      { field: packageFields.dateCreatedUtc!, header: 'Created', sortable: true, maxWidth: '120px' }
    ];
  }

  // ============================================================================
  // ENTITY-SPECIFIC PARAMETER MAPPING
  // ============================================================================

  protected override mapEntitySpecificParams(params: Params, request: IGetPackagesRequest): void {
    const paramsMap = nameOf<IGetPackagesRequest>();

    // Package type filter
    if (params[paramsMap['packageType']!] !== undefined && params[paramsMap['packageType']!] !== 'null') {
      request.packageType = parseInt(params[paramsMap['packageType']!], 10) as IPackageTypeEnum;
    }

    // Package status filter
    if (params[paramsMap['packageStatus']!] !== undefined && params[paramsMap['packageStatus']!] !== 'null') {
      request.packageStatus = parseInt(params[paramsMap['packageStatus']!], 10) as IPackageStatusEnum;
    }

    // Date filters
    if (params[paramsMap['purchasedFrom']!] !== undefined && params[paramsMap['purchasedFrom']!] !== 'null') {
      request.purchasedFrom = new Date(params[paramsMap['purchasedFrom']!]);
    }
    if (params[paramsMap['purchasedTo']!] !== undefined && params[paramsMap['purchasedTo']!] !== 'null') {
      request.purchasedTo = new Date(params[paramsMap['purchasedTo']!]);
    }
    if (params[paramsMap['expiresFrom']!] !== undefined && params[paramsMap['expiresFrom']!] !== 'null') {
      request.expiresFrom = new Date(params[paramsMap['expiresFrom']!]);
    }
    if (params[paramsMap['expiresTo']!] !== undefined && params[paramsMap['expiresTo']!] !== 'null') {
      request.expiresTo = new Date(params[paramsMap['expiresTo']!]);
    }

    // ID filters
    if (params[paramsMap['teacherId']!] !== undefined && params[paramsMap['teacherId']!] !== 'null') {
      request.teacherId = params[paramsMap['teacherId']!];
    }
    if (params[paramsMap['parentId']!] !== undefined && params[paramsMap['parentId']!] !== 'null') {
      request.parentId = params[paramsMap['parentId']!];
    }
    if (params[paramsMap['studentId']!] !== undefined && params[paramsMap['studentId']!] !== 'null') {
      request.studentId = params[paramsMap['studentId']!];
    }
    if (params[paramsMap['groupId']!] !== undefined && params[paramsMap['groupId']!] !== 'null') {
      request.groupId = params[paramsMap['groupId']!];
    }
    if (params[paramsMap['teachingLanguageId']!] !== undefined && params[paramsMap['teachingLanguageId']!] !== 'null') {
      request.teachingLanguageId = params[paramsMap['teachingLanguageId']!];
    }

    // Boolean filters
    if (params[paramsMap['hasAddOnExtension']!] !== undefined && params[paramsMap['hasAddOnExtension']!] !== 'null') {
      request.hasAddOnExtension = params[paramsMap['hasAddOnExtension']!] === 'true';
    }
    if (params[paramsMap['parentAccountDeleted']!] !== undefined && params[paramsMap['parentAccountDeleted']!] !== 'null') {
      request.parentAccountDeleted = params[paramsMap['parentAccountDeleted']!] === 'true';
    }
    if (params[paramsMap['hasOutstandingTeacherPayments']!] !== undefined) {
      request.hasOutstandingTeacherPayments = params[paramsMap['hasOutstandingTeacherPayments']!] === 'true';
    }
  }

  // ============================================================================
  // EXPORT FUNCTIONALITY
  // ============================================================================

  /**
   * Formats a single package record for CSV export
   * This contains all the package-specific formatting logic
   * Implementation of abstract method from BaseListHelperService
   */
  formatItemForExport(packageItem: ISearchPackageDto, cols: IDataGridFields[]): Record<string, string> {
    const exportObject: Record<string, string> = {};
    const packageFields = nameOf<ISearchPackageDto>();

    // Initialize all column fields to empty strings to ensure consistent structure
    for (const col of cols) {
      exportObject[col.field] = '';
    }

    // Format each field with proper display values using the same pattern as groups-list
    if (cols.some(col => col.field === packageFields.id)) {
      exportObject[packageFields.id] = this.getSafeStringValue(packageItem.id);
    }

    if (cols.some(col => col.field === packageFields.groupName!)) {
      exportObject[packageFields.groupName!] = packageItem.groupName || 'Individual Package';
    }

    if (cols.some(col => col.field === packageFields.teachingLanguageName)) {
      exportObject[packageFields.teachingLanguageName] = this.getSafeStringValue(packageItem.teachingLanguageName);
    }

    if (cols.some(col => col.field === packageFields.packageType)) {
      exportObject[packageFields.packageType] = this.getPackageTypeText(packageItem.packageType);
    }

    if (cols.some(col => col.field === packageFields.packageStatus)) {
      exportObject[packageFields.packageStatus] = this.getPackageStatusText(packageItem.packageStatus);
    }

    if (cols.some(col => col.field === packageFields.numberOfStudents)) {
      exportObject[packageFields.numberOfStudents] = this.getSafeStringValue(packageItem.numberOfStudents);
    }

    if (cols.some(col => col.field === 'students')) {
      exportObject['students'] = this.formatArrayForExport(packageItem.students, 'firstName');
    }

    if (cols.some(col => col.field === 'teacher')) {
      exportObject['teacher'] = packageItem.teacher
        ? `${packageItem.teacher.firstName} ${packageItem.teacher.lastName}`.trim()
        : 'No Teacher';
    }

    if (cols.some(col => col.field === 'lessonsProgress')) {
      const completedLessons = packageItem.totalLessons - packageItem.remainingLessons;
      exportObject['lessonsProgress'] = `${completedLessons}/${packageItem.totalLessons}`;
    }

    if (cols.some(col => col.field === packageFields.expiresOnDateUtc!)) {
      exportObject[packageFields.expiresOnDateUtc!] = this.formatDateForExport(packageItem.expiresOnDateUtc);
    }

    if (cols.some(col => col.field === packageFields.dateCreatedUtc!)) {
      exportObject[packageFields.dateCreatedUtc!] = this.formatDateForExport(packageItem.dateCreatedUtc);
    }

    if (cols.some(col => col.field === 'teacherPaymentStatus')) {
      if (packageItem.totalAmountForTeacher) {
        exportObject['teacherPaymentStatus'] =
          `Total: ${packageItem.totalAmountForTeacher}, ` +
          `Paid: ${packageItem.amountPaidToTeacher || 0}, ` +
          `Remaining: ${packageItem.amountRemainingForTeacher || 0}`;
      } else {
        exportObject['teacherPaymentStatus'] = 'N/A';
      }
    }

    if (cols.some(col => col.field === packageFields.hasBeenExtended)) {
      exportObject[packageFields.hasBeenExtended] = packageItem.hasBeenExtended ? 'Yes' : 'No';
    }

    if (cols.some(col => col.field === packageFields.validityInMonths)) {
      exportObject[packageFields.validityInMonths] = this.getSafeStringValue(packageItem.validityInMonths);
    }

    if (cols.some(col => col.field === 'parentAccountDeleted')) {
      exportObject['parentAccountDeleted'] = packageItem.parentAccountDeleted ? 'Yes' : 'No';
    }

    return exportObject;
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private getPackageTypeText(packageType: IPackageTypeEnum): string {
    // We need to inject GeneralService to use getEnumDisplayText
    // For now, let's create a simple mapping
    switch (packageType) {
      case IPackageTypeEnum.FreeTrial: return 'Free Trial';
      case IPackageTypeEnum.Paid: return 'Paid';
      case IPackageTypeEnum.Gift: return 'Gift';
      default: return 'Unknown';
    }
  }

  private getPackageStatusText(packageStatus: IPackageStatusEnum): string {
    // We need to inject GeneralService to use getEnumDisplayText
    // For now, let's create a simple mapping
    switch (packageStatus) {
      case IPackageStatusEnum.Active: return 'Active';
      case IPackageStatusEnum.InActive: return 'Inactive';
      case IPackageStatusEnum.Expired: return 'Expired';
      case IPackageStatusEnum.Completed: return 'Completed';
      case IPackageStatusEnum.Refunded: return 'Refunded';
      default: return 'Unknown';
    }
  }
}
