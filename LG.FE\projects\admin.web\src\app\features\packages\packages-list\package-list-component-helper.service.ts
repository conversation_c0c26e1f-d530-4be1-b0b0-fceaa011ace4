// ============================================================================
// PACKAGE LIST COMPONENT HELPER SERVICE
// ============================================================================

import { Injectable, inject } from '@angular/core';
import { Table } from 'primeng/table';
import { Params } from '@angular/router';
import {
  IGetPackagesRequest,
  IGetPackagesResponse,
  ISearchPackageDto,
  IDataGridFields,
  nameOf,
  EnumDropdownOptionsService,
  IPackageTypeEnum,
  IPackageStatusEnum,
  IEnumDropdownOptions
} from 'SharedModules.Library';
import moment from 'moment-timezone';
import { BaseListHelperService } from '../../../shared/services/base-list-helper.service';

const ISearchPackageDtoParamsMap = nameOf<ISearchPackageDto>();

/**
 * Helper service for packages list component
 * Provides utilities for request mapping, defaults, column configuration, and export functionality
 * Following the same pattern as TeacherListComponentHelperService and GroupsListComponentHelperService
 */
@Injectable({
  providedIn: 'root'
})
export class PackageListComponentHelperService extends BaseListHelperService<
  IGetPackagesRequest,
  IGetPackagesResponse,
  ISearchPackageDto
> {
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);

  // Default sort configuration - centralized in one place
  public static readonly DEFAULT_SORT_COLUMN = ISearchPackageDtoParamsMap.dateCreatedUtc!;
  public static readonly DEFAULT_SORT_DIRECTION = 'desc';
  public static readonly DEFAULT_PAGE_SIZE = 10;

  constructor() {
    super();
  }

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATIONS - Required by BaseListHelperService
  // ============================================================================

  getDefaultSortColumn(): string {
    return PackageListComponentHelperService.DEFAULT_SORT_COLUMN;
  }

  getDefaultSortDirection(): 'asc' | 'desc' {
    return PackageListComponentHelperService.DEFAULT_SORT_DIRECTION as 'asc' | 'desc';
  }

  getDefaultPageSize(): number {
    return PackageListComponentHelperService.DEFAULT_PAGE_SIZE;
  }

  getEntityName(): string {
    return 'packages';
  }

  getFieldNames(): Record<string, string> {
    return nameOf<IGetPackagesRequest>();
  }

  createDefaultRequest(): IGetPackagesRequest {
    return {
      pageNumber: 1,
      pageSize: this.getDefaultPageSize(),
      sortColumn: this.getDefaultSortColumn(),
      sortDirection: this.getDefaultSortDirection(),
      searchTerm: null,
      packageType: null,
      packageStatus: null,
      purchasedFrom: null,
      purchasedTo: null,
      expiresFrom: null,
      expiresTo: null,
      teacherId: null,
      parentId: null,
      studentId: null,
      groupId: null,
      teachingLanguageId: null,
      hasAddOnExtension: null,
      parentAccountDeleted: null,
      hasOutstandingTeacherPayments: false
    };
  }

  initializeTableColumns(): IDataGridFields[] {
    return [
      this.createColumn({
        field: ISearchPackageDtoParamsMap.id,
        header: 'Package ID',
        sortable: true,
        maxWidth: '120px'
      }),
      this.createColumn({
        field: ISearchPackageDtoParamsMap.groupName!,
        header: 'Package Name',
        sortable: true,
        maxWidth: '200px'
      }),
      this.createColumn({
        field: ISearchPackageDtoParamsMap.packageType,
        header: 'Type',
        sortable: true,
        maxWidth: '100px'
      }),
      this.createColumn({
        field: ISearchPackageDtoParamsMap.packageStatus,
        header: 'Status',
        sortable: true,
        maxWidth: '100px'
      }),
      this.createColumn({
        field: ISearchPackageDtoParamsMap.numberOfStudents,
        header: 'Students',
        sortable: true,
        maxWidth: '120px'
      }),
      this.createColumn({
        field: 'teacher',
        header: 'Teacher',
        sortable: false,
        maxWidth: '150px'
      }),
      this.createColumn({
        field: 'lessonsProgress',
        header: 'Lessons Progress',
        sortable: false,
        maxWidth: '140px'
      }),
      this.createColumn({
        field: ISearchPackageDtoParamsMap.expiresOnDateUtc!,
        header: 'Expires On',
        sortable: true,
        maxWidth: '120px'
      }),
      this.createColumn({
        field: ISearchPackageDtoParamsMap.dateCreatedUtc!,
        header: 'Created',
        sortable: true,
        maxWidth: '120px'
      }),
      this.createColumn({
        field: 'teacherPaymentStatus',
        header: 'Teacher Payment',
        sortable: false,
        maxWidth: '140px'
      }),
      this.createColumn({
        field: 'actions',
        header: 'Actions',
        sortable: false,
        maxWidth: '100px'
      })
    ];
  }

  // ============================================================================
  // ENTITY-SPECIFIC PARAMETER MAPPING
  // ============================================================================

  protected override mapEntitySpecificParams(params: Params, request: IGetPackagesRequest): void {
    const fieldNames = this.getFieldNames();

    // Package type filter
    if (params[fieldNames.packageType!] !== undefined && params[fieldNames.packageType!] !== 'null') {
      request.packageType = parseInt(params[fieldNames.packageType!], 10) as IPackageTypeEnum;
    }

    // Package status filter
    if (params[fieldNames.packageStatus!] !== undefined && params[fieldNames.packageStatus!] !== 'null') {
      request.packageStatus = parseInt(params[fieldNames.packageStatus!], 10) as IPackageStatusEnum;
    }

    // Date filters
    if (params[fieldNames.purchasedFrom!] !== undefined && params[fieldNames.purchasedFrom!] !== 'null') {
      request.purchasedFrom = new Date(params[fieldNames.purchasedFrom!]);
    }
    if (params[fieldNames.purchasedTo!] !== undefined && params[fieldNames.purchasedTo!] !== 'null') {
      request.purchasedTo = new Date(params[fieldNames.purchasedTo!]);
    }
    if (params[fieldNames.expiresFrom!] !== undefined && params[fieldNames.expiresFrom!] !== 'null') {
      request.expiresFrom = new Date(params[fieldNames.expiresFrom!]);
    }
    if (params[fieldNames.expiresTo!] !== undefined && params[fieldNames.expiresTo!] !== 'null') {
      request.expiresTo = new Date(params[fieldNames.expiresTo!]);
    }

    // ID filters
    if (params[fieldNames.teacherId!] !== undefined && params[fieldNames.teacherId!] !== 'null') {
      request.teacherId = params[fieldNames.teacherId!];
    }
    if (params[fieldNames.parentId!] !== undefined && params[fieldNames.parentId!] !== 'null') {
      request.parentId = params[fieldNames.parentId!];
    }
    if (params[fieldNames.studentId!] !== undefined && params[fieldNames.studentId!] !== 'null') {
      request.studentId = params[fieldNames.studentId!];
    }
    if (params[fieldNames.groupId!] !== undefined && params[fieldNames.groupId!] !== 'null') {
      request.groupId = params[fieldNames.groupId!];
    }
    if (params[fieldNames.teachingLanguageId!] !== undefined && params[fieldNames.teachingLanguageId!] !== 'null') {
      request.teachingLanguageId = params[fieldNames.teachingLanguageId!];
    }

    // Boolean filters
    if (params[fieldNames.hasAddOnExtension!] !== undefined && params[fieldNames.hasAddOnExtension!] !== 'null') {
      request.hasAddOnExtension = params[fieldNames.hasAddOnExtension!] === 'true';
    }
    if (params[fieldNames.parentAccountDeleted!] !== undefined && params[fieldNames.parentAccountDeleted!] !== 'null') {
      request.parentAccountDeleted = params[fieldNames.parentAccountDeleted!] === 'true';
    }
    if (params[fieldNames.hasOutstandingTeacherPayments!] !== undefined) {
      request.hasOutstandingTeacherPayments = params[fieldNames.hasOutstandingTeacherPayments!] === 'true';
    }
  }

  // ============================================================================
  // EXPORT FUNCTIONALITY
  // ============================================================================

  /**
   * Formats a single package record for CSV export
   * This contains all the package-specific formatting logic
   * Implementation of abstract method from BaseListHelperService
   */
  formatItemForExport(packageItem: ISearchPackageDto, cols: IDataGridFields[]): Record<string, string> {
    const exportObject: Record<string, string> = {};
    const packageFields = nameOf<ISearchPackageDto>();

    // Initialize all column fields to empty strings to ensure consistent structure
    for (const col of cols) {
      exportObject[col.field] = '';
    }

    // Map package-specific fields
    exportObject[packageFields.id] = this.getSafeStringValue(packageItem.id);
    exportObject[packageFields.groupName!] = packageItem.groupName || 'Individual Package';
    exportObject[packageFields.teachingLanguageName] = this.getSafeStringValue(packageItem.teachingLanguageName);
    
    // Package type and status with enum text
    exportObject[packageFields.packageType] = this.getPackageTypeText(packageItem.packageType);
    exportObject[packageFields.packageStatus] = this.getPackageStatusText(packageItem.packageStatus);
    
    // Student information
    exportObject[packageFields.numberOfStudents] = this.getSafeStringValue(packageItem.numberOfStudents);
    exportObject['students'] = this.formatArrayForExport(packageItem.students, 'firstName');
    
    // Teacher information
    exportObject['teacher'] = packageItem.teacher 
      ? `${packageItem.teacher.firstName} ${packageItem.teacher.lastName}`.trim()
      : 'No Teacher';
    
    // Lessons progress
    const completedLessons = packageItem.totalLessons - packageItem.remainingLessons;
    exportObject['lessonsProgress'] = `${completedLessons}/${packageItem.totalLessons}`;
    
    // Dates
    exportObject[packageFields.expiresOnDateUtc!] = this.formatDateForExport(packageItem.expiresOnDateUtc);
    exportObject[packageFields.dateCreatedUtc!] = this.formatDateForExport(packageItem.dateCreatedUtc);
    
    // Teacher payment information
    if (packageItem.totalAmountForTeacher) {
      exportObject['teacherPaymentStatus'] = 
        `Total: ${packageItem.totalAmountForTeacher}, ` +
        `Paid: ${packageItem.amountPaidToTeacher || 0}, ` +
        `Remaining: ${packageItem.amountRemainingForTeacher || 0}`;
    } else {
      exportObject['teacherPaymentStatus'] = 'N/A';
    }

    // Additional fields
    exportObject[packageFields.hasBeenExtended] = packageItem.hasBeenExtended ? 'Yes' : 'No';
    exportObject[packageFields.validityInMonths] = this.getSafeStringValue(packageItem.validityInMonths);
    exportObject['parentAccountDeleted'] = packageItem.parentAccountDeleted ? 'Yes' : 'No';

    return exportObject;
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private getPackageTypeText(packageType: IPackageTypeEnum): string {
    // We need to inject GeneralService to use getEnumDisplayText
    // For now, let's create a simple mapping
    switch (packageType) {
      case IPackageTypeEnum.FreeTrial: return 'Free Trial';
      case IPackageTypeEnum.Paid: return 'Paid';
      case IPackageTypeEnum.Gift: return 'Gift';
      default: return 'Unknown';
    }
  }

  private getPackageStatusText(packageStatus: IPackageStatusEnum): string {
    // We need to inject GeneralService to use getEnumDisplayText
    // For now, let's create a simple mapping
    switch (packageStatus) {
      case IPackageStatusEnum.Active: return 'Active';
      case IPackageStatusEnum.InActive: return 'Inactive';
      case IPackageStatusEnum.Expired: return 'Expired';
      case IPackageStatusEnum.Completed: return 'Completed';
      case IPackageStatusEnum.Refunded: return 'Refunded';
      default: return 'Unknown';
    }
  }
}
