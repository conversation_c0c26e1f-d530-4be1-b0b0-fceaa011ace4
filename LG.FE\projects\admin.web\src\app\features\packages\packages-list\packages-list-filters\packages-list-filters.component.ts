// ============================================================================
// PACKAGES LIST FILTERS COMPONENT
// ============================================================================

import { CommonModule } from '@angular/common';
import { Component, input, output, OnInit, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';

// === PRIMENG IMPORTS ===
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { InputTextModule } from 'primeng/inputtext';
import { CheckboxModule } from 'primeng/checkbox';

// === SHARED LIBRARY IMPORTS ===
import {
  IGetPackagesRequest,
  IPackageTypeEnum,
  IPackageStatusEnum,
  IEnumDropdownOptions,
  nameOf,
  PrimeStudentsSelectionComponent,
  PrimeTeachersSelectionComponent
} from 'SharedModules.Library';

// ============================================================================
// INTERFACES
// ============================================================================

export interface IPackagesFilterChangeEvent {
  filters: Partial<IGetPackagesRequest>;
}

export interface IPackagesFilterActionEvent {
  action: 'apply' | 'reset';
  filters: Partial<IGetPackagesRequest>;
}

// ============================================================================
// COMPONENT DEFINITION
// ============================================================================

/**
 * Packages List Filters Component
 *
 * Provides filtering capabilities for the packages list including:
 * - Package type and status filters
 * - Date range filters (purchased, expires)
 * - Teacher and student selection
 * - Search functionality
 * - Teaching language filter
 */
@Component({
  selector: 'app-packages-list-filters',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    CalendarModule,
    DropdownModule,
    MultiSelectModule,
    InputTextModule,
    CheckboxModule,
    PrimeStudentsSelectionComponent,
    PrimeTeachersSelectionComponent
  ],
  templateUrl: './packages-list-filters.component.html',
  styleUrls: ['./packages-list-filters.component.scss']
})
export class PackagesListFiltersComponent implements OnInit {

  // ============================================================================
  // INPUTS
  // ============================================================================

  packageTypes = input.required<IEnumDropdownOptions[]>();
  packageStatuses = input.required<IEnumDropdownOptions[]>();

  // ============================================================================
  // OUTPUTS
  // ============================================================================

  filtersChange = output<IPackagesFilterChangeEvent>();
  filtersAction = output<IPackagesFilterActionEvent>();

  // ============================================================================
  // COMPONENT PROPERTIES
  // ============================================================================

  // Field names for type safety
  private readonly fieldNames = nameOf<IGetPackagesRequest>();

  // Filter form properties
  searchTerm = signal<string>('');
  selectedPackageTypes = signal<IPackageTypeEnum[]>([]);
  selectedPackageStatuses = signal<IPackageStatusEnum[]>([]);
  selectedTeacher = signal<string | null>(null);
  selectedStudent = signal<string | null>(null);
  selectedGroup = signal<string | null>(null);
  purchasedFromDate = signal<Date | null>(null);
  purchasedToDate = signal<Date | null>(null);
  expiresFromDate = signal<Date | null>(null);
  expiresToDate = signal<Date | null>(null);
  hasAddOnExtension = signal<boolean | null>(null);
  parentAccountDeleted = signal<boolean | null>(null);
  hasOutstandingTeacherPayments = signal<boolean>(false);

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  ngOnInit(): void {
    // Initialize filters from query params if needed
  }

  // ============================================================================
  // FILTER MANAGEMENT METHODS
  // ============================================================================

  buildFiltersObject(): Partial<IGetPackagesRequest> {
    const filters: Partial<IGetPackagesRequest> = {};

    // Search term
    if (this.searchTerm()) {
      filters.searchTerm = this.searchTerm();
    }

    // Package types
    if (this.selectedPackageTypes().length === 1) {
      filters.packageType = this.selectedPackageTypes()[0];
    }

    // Package statuses
    if (this.selectedPackageStatuses().length === 1) {
      filters.packageStatus = this.selectedPackageStatuses()[0];
    }

    // Teacher, student, group
    if (this.selectedTeacher()) {
      filters.teacherId = this.selectedTeacher();
    }
    if (this.selectedStudent()) {
      filters.studentId = this.selectedStudent();
    }
    if (this.selectedGroup()) {
      filters.groupId = this.selectedGroup();
    }

    // Date ranges
    if (this.purchasedFromDate()) {
      filters.purchasedFrom = this.purchasedFromDate();
    }
    if (this.purchasedToDate()) {
      filters.purchasedTo = this.purchasedToDate();
    }
    if (this.expiresFromDate()) {
      filters.expiresFrom = this.expiresFromDate();
    }
    if (this.expiresToDate()) {
      filters.expiresTo = this.expiresToDate();
    }

    // Boolean filters
    if (this.hasAddOnExtension() !== null) {
      filters.hasAddOnExtension = this.hasAddOnExtension();
    }
    if (this.parentAccountDeleted() !== null) {
      filters.parentAccountDeleted = this.parentAccountDeleted();
    }
    if (this.hasOutstandingTeacherPayments()) {
      filters.hasOutstandingTeacherPayments = this.hasOutstandingTeacherPayments();
    }

    return filters;
  }

  // ============================================================================
  // ACTION METHODS
  // ============================================================================

  onApplyFilters(): void {
    const filters = this.buildFiltersObject();
    this.filtersAction.emit({ action: 'apply', filters });
  }

  onResetFilters(): void {
    this.resetAllFilters();
    const filters = this.buildFiltersObject();
    this.filtersAction.emit({ action: 'reset', filters });
  }

  resetAllFilters(): void {
    this.searchTerm.set('');
    this.selectedPackageTypes.set([]);
    this.selectedPackageStatuses.set([]);
    this.selectedTeacher.set(null);
    this.selectedStudent.set(null);
    this.selectedGroup.set(null);
    this.purchasedFromDate.set(null);
    this.purchasedToDate.set(null);
    this.expiresFromDate.set(null);
    this.expiresToDate.set(null);
    this.hasAddOnExtension.set(null);
    this.parentAccountDeleted.set(null);
    this.hasOutstandingTeacherPayments.set(false);
  }

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  onTeacherSelected(teacher: any): void {
    // Extract teacher ID from the selected teacher object
    const teacherId = teacher?.id || teacher?.userId || null;
    this.selectedTeacher.set(teacherId);
    this.emitFiltersChange();
  }

  onStudentSelected(student: any): void {
    // Extract student ID from the selected student object
    const studentId = student?.id || student?.userId || null;
    this.selectedStudent.set(studentId);
    this.emitFiltersChange();
  }

  emitFiltersChange(): void {
    const filters = this.buildFiltersObject();
    this.filtersChange.emit({ filters });
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  getBooleanOptions() {
    return [
      { label: 'Yes', value: true },
      { label: 'No', value: false }
    ];
  }

  hasActiveFilters(): boolean {
    return this.searchTerm() !== '' ||
           this.selectedPackageTypes().length > 0 ||
           this.selectedPackageStatuses().length > 0 ||
           this.selectedTeacher() !== null ||
           this.selectedStudent() !== null ||
           this.selectedGroup() !== null ||
           this.purchasedFromDate() !== null ||
           this.purchasedToDate() !== null ||
           this.expiresFromDate() !== null ||
           this.expiresToDate() !== null ||
           this.hasAddOnExtension() !== null ||
           this.parentAccountDeleted() !== null ||
           this.hasOutstandingTeacherPayments() !== false;
  }
}
