<!-- ============================================================================ -->
<!-- PACKAGES LIST COMPONENT TEMPLATE -->
<!-- ============================================================================ -->

<div class="packages-page-container">
  <!-- Main Content (Table & Applied Filters) -->
  <div class="main-content">
    <!-- Applied Filters Section -->
    <app-applied-filters-tags
      [filters]="appliedFilters()"
      (filterRemoved)="onAppliedFilterRemove($event)"
      (clearAllClicked)="onAppliedFiltersClearAll($event)">
           <p-button extraButton
                icon="pi pi-filter"
                label="Filters"
                severity="secondary"
                (click)="openFiltersDrawer()"
                class=""
                styleClass="p-button-sm"
                [badge]="appliedFiltersCount() > 0 ? appliedFiltersCount().toString() : undefined"
                badgeClass="p-badge-info" />
    </app-applied-filters-tags>

    <!-- Filters Drawer Sidebar -->
    <app-filters-drawer-sidebar
      #filtersDrawer
      [(visible)]="isFiltersDrawerVisible"
      [config]="filtersDrawerConfig"
      [filterContentTemplate]="filterContentTemplate"
      (actionClicked)="onFiltersDrawerAction($event)">
    </app-filters-drawer-sidebar>

    <!-- Filter Content Template -->
    <ng-template #filterContentTemplate>
      <app-packages-list-filters
        #packagesFilters
        [packageTypes]="packageTypes()"
        [packageStatuses]="packageStatuses()"
        (filtersChange)="onFiltersChange($event)"
        (filtersAction)="onFiltersAction($event)">
      </app-packages-list-filters>
    </ng-template>

    <!-- Table Container -->
    <div class="table-container surface-card p-3 border-round">
    <!-- Data Table -->
    <div class="table-container">
      <p-table #dt [value]="dataResponse()?.pageData || []" dataKey="id" [lazy]="true" [paginator]="true"
        [rows]="queryParams().pageSize" [totalRecords]="totalRecords()" [loading]="isLoading()"
        [rowsPerPageOptions]="rowsPerPageOptions()" [showCurrentPageReport]="true"
        [sortField]="queryParams().sortColumn" [sortOrder]="queryParams().sortDirection === 'asc' ? 1 : -1"
        [reorderableColumns]="true" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} packages"
        (onPage)="onPageChange($event)" (onSort)="onSortChange($event)" (onColReorder)="onColumnReorder($event)"
        [scrollable]="true" scrollHeight="flex"
        [resizableColumns]="true" showGridlines stripedRows

        <!-- Table Header -->
        <ng-template pTemplate="header">
          <tr>
            @for (col of cols; track col.field) {
              <th
                [pSortableColumn]="col.sortable ? col.field : null"
                [style.max-width]="col.maxWidth"
                [ngClass]="{'text-center': col.field === 'actions'}">
                {{ col.header }}
                @if (col.sortable) {
                  <p-sortIcon [field]="col.field"></p-sortIcon>
                }
              </th>
            }
          </tr>
        </ng-template>

        <!-- Table Body -->
        <ng-template pTemplate="body" let-package let-rowIndex="rowIndex">
          <tr>
            @for (col of cols; track col.field) {
              <td [style.max-width]="col.maxWidth">
                @switch (col.field) {
                  
                  <!-- Package ID -->
                  @case ('id') {
                    <span class="font-mono text-sm">{{ package.id | slice:0:8 }}...</span>
                  }

                  <!-- Package Name/Group -->
                  @case ('groupName') {
                    <div class="package-name-cell">
                      @if (package.groupName) {
                        <span class="font-semibold">{{ package.groupName }}</span>
                      } @else {
                        <span class="text-muted">Individual Package</span>
                      }
                      <div class="text-sm text-muted">{{ package.teachingLanguageName }}</div>
                    </div>
                  }

                  <!-- Package Type -->
                  @case ('packageType') {
                    <span class="package-type-badge"
                          [ngClass]="'package-type-' + package.packageType">
                      {{ getPackageTypeText(package.packageType) }}
                    </span>
                  }

                  <!-- Package Status -->
                  @case ('packageStatus') {
                    <span class="package-status-badge"
                          [ngClass]="'package-status-' + package.packageStatus">
                      {{ getPackageStatusText(package.packageStatus) }}
                    </span>
                  }

                  <!-- Students -->
                  @case ('numberOfStudents') {
                    <div class="students-cell">
                      <span class="student-count">{{ package.numberOfStudents }}</span>
                      @if (package.students && package.students.length > 0) {
                        <div class="student-names text-sm text-muted">
                          {{ getStudentNames(package.students) }}
                        </div>
                      }
                    </div>
                  }

                  <!-- Teacher -->
                  @case ('teacher') {
                    @if (package.teacher) {
                      <div class="teacher-cell">
                        <span class="teacher-name">
                          {{ package.teacher.firstName }} {{ package.teacher.lastName }}
                        </span>
                      </div>
                    } @else {
                      <span class="text-muted">No Teacher</span>
                    }
                  }

                  <!-- Lessons Progress -->
                  @case ('lessonsProgress') {
                    <div class="lessons-progress-cell">
                      <div class="progress-text">
                        {{ package.totalLessons - package.remainingLessons }}/{{ package.totalLessons }}
                      </div>
                      <div class="progress-bar-container">
                        <div class="progress-bar"
                             [style.width.%]="getLessonsProgressPercentage(package)">
                        </div>
                      </div>
                    </div>
                  }

                  <!-- Expiration Date -->
                  @case ('expiresOnDateUtc') {
                    @if (package.expiresOnDateUtc) {
                      <div class="expiration-cell">
                        <span [ngClass]="getExpirationClass(package.expiresOnDateUtc)">
                          {{ formatDate(package.expiresOnDateUtc) }}
                        </span>
                        @if (isExpiringSoon(package.expiresOnDateUtc)) {
                          <i class="pi pi-exclamation-triangle text-orange-500 ml-1"></i>
                        }
                      </div>
                    } @else {
                      <span class="text-muted">No Expiration</span>
                    }
                  }

                  <!-- Created Date -->
                  @case ('dateCreatedUtc') {
                    @if (package.dateCreatedUtc) {
                      <span>{{ formatDate(package.dateCreatedUtc) }}</span>
                    }
                  }

                  <!-- Teacher Payment Status -->
                  @case ('teacherPaymentStatus') {
                    <div class="payment-status-cell">
                      @if (package.totalAmountForTeacher) {
                        <div class="payment-amounts text-sm">
                          <div>Total: {{ formatCurrency(package.totalAmountForTeacher) }}</div>
                          <div>Paid: {{ formatCurrency(package.amountPaidToTeacher || 0) }}</div>
                          <div>Remaining: {{ formatCurrency(package.amountRemainingForTeacher || 0) }}</div>
                        </div>
                      } @else {
                        <span class="text-muted">N/A</span>
                      }
                    </div>
                  }

                  <!-- Actions -->
                  @case ('actions') {
                    <div class="actions-cell text-center">
                      <button
                        type="button"
                        class="p-button p-button-sm p-button-text p-button-rounded"
                        pButton
                        icon="pi pi-eye"
                        pTooltip="View Package Details"
                        (click)="onViewPackage(package)">
                      </button>
                      <button
                        type="button"
                        class="p-button p-button-sm p-button-text p-button-rounded"
                        pButton
                        icon="pi pi-pencil"
                        pTooltip="Edit Package"
                        (click)="onEditPackage(package)">
                      </button>
                    </div>
                  }

                  <!-- Default case for other fields -->
                  @default {
                    <span>{{ getFieldValue(package, col.field) }}</span>
                  }
                }
              </td>
            }
          </tr>
        </ng-template>

        <!-- Empty State -->
        <ng-template pTemplate="emptymessage">
          <tr>
            <td [attr.colspan]="cols.length" class="text-center py-4">
              @if (isLoading()) {
                <div class="loading-message">
                  <i class="pi pi-spin pi-spinner mr-2"></i>
                  Loading packages...
                </div>
              } @else {
                <div class="empty-state">
                  <i class="pi pi-inbox text-4xl text-muted mb-3"></i>
                  <h3 class="text-lg font-semibold mb-2">No packages found</h3>
                  <p class="text-muted">
                    @if (hasActiveFilters()) {
                      Try adjusting your filters to see more results.
                    } @else {
                      No packages have been created yet.
                    }
                  </p>
                </div>
              }
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>
