<!-- ============================================================================ -->
<!-- PACKAGES LIST COMPONENT TEMPLATE -->
<!-- ============================================================================ -->

<div class="packages-list-container">
  <!-- Header with search and actions -->
  <app-data-grid-header-footer
    [title]="getEntityDisplayName()"
    [searchValue]="searchValue()"
    [loading]="loading()"
    [totalRecords]="totalRecords()"
    [showCreateButton]="false"
    [showExportButton]="true"
    [showFiltersButton]="true"
    [filtersCount]="appliedFilters().length"
    (searchChange)="onSearchChange($event)"
    (exportClick)="onExport()"
    (filtersClick)="toggleFilters()"
    (createClick)="onCreateClick()">
  </app-data-grid-header-footer>

  <!-- Applied Filters Tags -->
  @if (appliedFilters().length > 0) {
    <div class="applied-filters-container mb-3">
      <lib-applied-filters-tags
        [appliedFilters]="appliedFilters()"
        (removeFilter)="onRemoveAppliedFilter($event)"
        (clearAllFilters)="onClearAllFilters()">
      </lib-applied-filters-tags>
    </div>
  }

  <!-- Main Content Area -->
  <div class="content-wrapper">
    <!-- Data Table -->
    <div class="table-container">
      <p-table
        #dt
        [value]="data()"
        [columns]="cols()"
        [totalRecords]="totalRecords()"
        [rows]="pageSize()"
        [first]="first()"
        [loading]="loading()"
        [paginator]="true"
        [lazy]="true"
        [sortField]="sortField()"
        [sortOrder]="sortOrder()"
        [showCurrentPageReport]="true"
        [rowsPerPageOptions]="[10, 25, 50, 100]"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} packages"
        styleClass="p-datatable-sm p-datatable-striped"
        responsiveLayout="scroll"
        (onLazyLoad)="onLazyLoad($event)"
        (onSort)="onSort($event)"
        (onPage)="onPage($event)">

        <!-- Table Header -->
        <ng-template pTemplate="header" let-columns>
          <tr>
            @for (col of columns; track col.field) {
              <th
                [pSortableColumn]="col.sortable ? col.field : null"
                [style.max-width]="col.maxWidth"
                [ngClass]="{'text-center': col.field === 'actions'}">
                {{ col.header }}
                @if (col.sortable) {
                  <p-sortIcon [field]="col.field"></p-sortIcon>
                }
              </th>
            }
          </tr>
        </ng-template>

        <!-- Table Body -->
        <ng-template pTemplate="body" let-package let-rowIndex="rowIndex">
          <tr>
            @for (col of cols(); track col.field) {
              <td [style.max-width]="col.maxWidth">
                @switch (col.field) {
                  
                  <!-- Package ID -->
                  @case ('id') {
                    <span class="font-mono text-sm">{{ package.id | slice:0:8 }}...</span>
                  }

                  <!-- Package Name/Group -->
                  @case ('groupName') {
                    <div class="package-name-cell">
                      @if (package.groupName) {
                        <span class="font-semibold">{{ package.groupName }}</span>
                      } @else {
                        <span class="text-muted">Individual Package</span>
                      }
                      <div class="text-sm text-muted">{{ package.teachingLanguageName }}</div>
                    </div>
                  }

                  <!-- Package Type -->
                  @case ('packageType') {
                    <span class="package-type-badge"
                          [ngClass]="'package-type-' + package.packageType">
                      {{ getPackageTypeText(package.packageType) }}
                    </span>
                  }

                  <!-- Package Status -->
                  @case ('packageStatus') {
                    <span class="package-status-badge"
                          [ngClass]="'package-status-' + package.packageStatus">
                      {{ getPackageStatusText(package.packageStatus) }}
                    </span>
                  }

                  <!-- Students -->
                  @case ('numberOfStudents') {
                    <div class="students-cell">
                      <span class="student-count">{{ package.numberOfStudents }}</span>
                      @if (package.students && package.students.length > 0) {
                        <div class="student-names text-sm text-muted">
                          {{ getStudentNames(package.students) }}
                        </div>
                      }
                    </div>
                  }

                  <!-- Teacher -->
                  @case ('teacher') {
                    @if (package.teacher) {
                      <div class="teacher-cell">
                        <span class="teacher-name">
                          {{ package.teacher.firstName }} {{ package.teacher.lastName }}
                        </span>
                      </div>
                    } @else {
                      <span class="text-muted">No Teacher</span>
                    }
                  }

                  <!-- Lessons Progress -->
                  @case ('lessonsProgress') {
                    <div class="lessons-progress-cell">
                      <div class="progress-text">
                        {{ package.totalLessons - package.remainingLessons }}/{{ package.totalLessons }}
                      </div>
                      <div class="progress-bar-container">
                        <div class="progress-bar"
                             [style.width.%]="getLessonsProgressPercentage(package)">
                        </div>
                      </div>
                    </div>
                  }

                  <!-- Expiration Date -->
                  @case ('expiresOnDateUtc') {
                    @if (package.expiresOnDateUtc) {
                      <div class="expiration-cell">
                        <span [ngClass]="getExpirationClass(package.expiresOnDateUtc)">
                          {{ formatDate(package.expiresOnDateUtc) }}
                        </span>
                        @if (isExpiringSoon(package.expiresOnDateUtc)) {
                          <i class="pi pi-exclamation-triangle text-orange-500 ml-1"></i>
                        }
                      </div>
                    } @else {
                      <span class="text-muted">No Expiration</span>
                    }
                  }

                  <!-- Created Date -->
                  @case ('dateCreatedUtc') {
                    @if (package.dateCreatedUtc) {
                      <span>{{ formatDate(package.dateCreatedUtc) }}</span>
                    }
                  }

                  <!-- Teacher Payment Status -->
                  @case ('teacherPaymentStatus') {
                    <div class="payment-status-cell">
                      @if (package.totalAmountForTeacher) {
                        <div class="payment-amounts text-sm">
                          <div>Total: {{ formatCurrency(package.totalAmountForTeacher) }}</div>
                          <div>Paid: {{ formatCurrency(package.amountPaidToTeacher || 0) }}</div>
                          <div>Remaining: {{ formatCurrency(package.amountRemainingForTeacher || 0) }}</div>
                        </div>
                      } @else {
                        <span class="text-muted">N/A</span>
                      }
                    </div>
                  }

                  <!-- Actions -->
                  @case ('actions') {
                    <div class="actions-cell text-center">
                      <button
                        type="button"
                        class="p-button p-button-sm p-button-text p-button-rounded"
                        pButton
                        icon="pi pi-eye"
                        pTooltip="View Package Details"
                        (click)="onViewPackage(package)">
                      </button>
                      <button
                        type="button"
                        class="p-button p-button-sm p-button-text p-button-rounded"
                        pButton
                        icon="pi pi-pencil"
                        pTooltip="Edit Package"
                        (click)="onEditPackage(package)">
                      </button>
                    </div>
                  }

                  <!-- Default case for other fields -->
                  @default {
                    <span>{{ getFieldValue(package, col.field) }}</span>
                  }
                }
              </td>
            }
          </tr>
        </ng-template>

        <!-- Empty State -->
        <ng-template pTemplate="emptymessage">
          <tr>
            <td [attr.colspan]="cols().length" class="text-center py-4">
              @if (loading()) {
                <div class="loading-message">
                  <i class="pi pi-spin pi-spinner mr-2"></i>
                  Loading packages...
                </div>
              } @else {
                <div class="empty-state">
                  <i class="pi pi-inbox text-4xl text-muted mb-3"></i>
                  <h3 class="text-lg font-semibold mb-2">No packages found</h3>
                  <p class="text-muted">
                    @if (hasActiveFilters()) {
                      Try adjusting your filters to see more results.
                    } @else {
                      No packages have been created yet.
                    }
                  </p>
                </div>
              }
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>

    <!-- Filters Sidebar -->
    <lib-filters-drawer-sidebar
      [config]="filtersDrawerConfig()"
      (closeFilters)="closeFilters()">
      <app-packages-list-filters
        [packageTypes]="packageTypes()"
        [packageStatuses]="packageStatuses()"
        (filtersChange)="onFiltersChange($event)"
        (filtersAction)="onFiltersAction($event)">
      </app-packages-list-filters>
    </lib-filters-drawer-sidebar>
  </div>
</div>
